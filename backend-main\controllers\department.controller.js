const Department = require("../models/department.model");

exports.addDepartment = async (req, res) => {
  try {
    const { name, code } = req.body;

    const existingDepartment = await Department.findOne({
      $or: [{ name: name }, { code: code }],
    });
    if (existingDepartment) {
      return res.status(400).json({
        status: "failed",
        message: "Department code or name already exist",
      });
    }

    await Department.create({ name, code });

    res
      .status(201)
      .json({ status: "success", message: "Department added successfully!!" });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.getAllDepartments = async (req, res) => {
  try {
    const departments = await Department.find({});
    res.status(200).json({ status: "sucess", departments });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedDepartment = await Department.findByIdAndDelete(id);

    if (!deletedDepartment) {
      return res
        .status(404)
        .json({ status: "failed", message: "Department not found!!" });
    }

    res.status(200).json({
      status: "success",
      message: "Department deleted successfully!!",
    });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    const { name, code } = req.body;

    const existingDepartment = await Department.findOne({
      $or: [{ name: name }, { code: code }],
    });
    if (existingDepartment) {
      return res
        .status(400)
        .json({ status: "failed", message: "Department already exist" });
    }

    const updatedDepartment = await Department.findByIdAndUpdate(
      id,
      { name, code },
      { $new: true }
    );

    if (!updatedDepartment) {
      return res
        .status(404)
        .json({ status: "failed", message: "Department not found!!" });
    }

    res.status(200).json({
      status: "success",
      message: "Department updated successfully!!",
    });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};
