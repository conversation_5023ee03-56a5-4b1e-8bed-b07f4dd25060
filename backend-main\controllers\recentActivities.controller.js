const Activity = require("../models/recentActivities.model");

// Get recent activities for the current user
exports.getRecentActivities = async (req, res) => {
  try {
    const userId = req.user._id;

    const activities = await Activity.find({ user: userId })
      .populate('user', 'fullName email')
      .sort({ createdAt: -1 })
      .limit(10);

    res.status(200).json({ success: true, data: activities });
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

// Get all recent activities (for senior manager to see system-wide activities)
exports.getAllRecentActivities = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 5;
    const page = parseInt(req.query.page) || 1;
    const skip = (page - 1) * limit;

    // Get activities from all users
    const activities = await Activity.find({})
      .populate('user', 'fullName email role')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(skip);

    const total = await Activity.countDocuments({});

    res.status(200).json({
      success: true,
      data: activities,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching all recent activities:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};

// Get activities by entity (e.g., all activities for a specific project)
exports.getActivitiesByEntity = async (req, res) => {
  try {
    const { entityType, entityId } = req.params;

    const activities = await Activity.find({
      entityType,
      entityId
    })
      .populate('user', 'fullName email')
      .sort({ createdAt: -1 });

    res.status(200).json({ success: true, data: activities });
  } catch (error) {
    console.error("Error fetching entity activities:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};
