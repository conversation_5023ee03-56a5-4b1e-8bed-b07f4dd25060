{"name": "debug", "version": "2.6.9", "repository": {"type": "git", "url": "git://github.com/visionmedia/debug.git"}, "description": "small debugging utility", "keywords": ["debug", "log", "debugger"], "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>> (http://n8.io)", "<PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"browserify": "9.0.3", "chai": "^3.5.0", "concurrently": "^3.1.0", "coveralls": "^2.11.15", "eslint": "^3.12.1", "istanbul": "^0.4.5", "karma": "^1.3.0", "karma-chai": "^0.1.0", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-sinon": "^1.0.5", "mocha": "^3.2.0", "mocha-lcov-reporter": "^1.2.0", "rimraf": "^2.5.4", "sinon": "^1.17.6", "sinon-chai": "^2.8.0"}, "main": "./src/index.js", "browser": "./src/browser.js", "component": {"scripts": {"debug/index.js": "browser.js", "debug/debug.js": "debug.js"}}}