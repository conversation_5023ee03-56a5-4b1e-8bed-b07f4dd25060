const Project = require("../models/project.model");
const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");
const User = require("../models/user.model");
const Role = require("../models/roles.model");

class ReportingService {
  // Generate comprehensive project analytics
  static async generateProjectAnalytics(filters = {}) {
    try {
      const { startDate, endDate, status, district, sector } = filters;
      
      // Build query
      const query = {};
      if (startDate && endDate) {
        query.createdAt = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }
      if (status && status !== 'all') {
        query.status = status;
      }
      if (district && district !== 'all') {
        query.district = district;
      }
      if (sector && sector !== 'all') {
        query.sector = sector;
      }

      const projects = await Project.find(query)
        .populate('assignedTo', 'fullName')
        .populate('createdBy', 'fullName')
        .populate('district', 'name')
        .sort({ createdAt: -1 });

      // Calculate analytics
      const analytics = {
        totalProjects: projects.length,
        totalBudget: projects.reduce((sum, p) => sum + (p.budget || 0), 0),
        totalUsedBudget: projects.reduce((sum, p) => sum + ((p.budget || 0) - (p.remainingBudget || 0)), 0),
        totalRemainingBudget: projects.reduce((sum, p) => sum + (p.remainingBudget || 0), 0),
        statusBreakdown: this.calculateStatusBreakdown(projects),
        sectorBreakdown: this.calculateSectorBreakdown(projects),
        districtBreakdown: this.calculateDistrictBreakdown(projects),
        monthlyTrends: await this.calculateMonthlyTrends(query),
        budgetUtilization: this.calculateBudgetUtilization(projects),
        projectProgress: await this.calculateProjectProgress(projects),
      };

      // Calculate progress for each project
      const projectsWithProgress = [];
      for (const p of projects) {
        const progress = await this.calculateSingleProjectProgress(p._id);
        projectsWithProgress.push({
          id: p._id,
          name: p.name,
          budget: p.budget,
          remainingBudget: p.remainingBudget,
          usedBudget: (p.budget || 0) - (p.remainingBudget || 0),
          status: p.status,
          sector: p.sector,
          district: p.district?.name,
          assignedTo: p.assignedTo?.fullName,
          createdBy: p.createdBy?.fullName,
          startDate: p.startDate,
          endDate: p.endDate,
          createdAt: p.createdAt,
          progress
        });
      }

      return {
        analytics,
        projects: projectsWithProgress
      };
    } catch (error) {
      throw error;
    }
  }

  // Generate activity analytics
  static async generateActivityAnalytics(filters = {}) {
    try {
      const { startDate, endDate, status, projectId } = filters;
      
      const query = {};
      if (startDate && endDate) {
        query.createdAt = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }
      if (status && status !== 'all') {
        query.status = status;
      }
      if (projectId && projectId !== 'all') {
        query.project = projectId;
      }

      const activities = await ProjectActivities.find(query)
        .populate('project', 'name')
        .populate('assignedTo', 'fullName')
        .sort({ createdAt: -1 });

      // Get reports for these activities
      const activityIds = activities.map(a => a._id);
      const reports = await ActivityReport.find({
        activity: { $in: activityIds }
      }).populate('activity');

      const analytics = {
        totalActivities: activities.length,
        totalBudget: activities.reduce((sum, a) => sum + (a.budget || 0), 0),
        statusBreakdown: this.calculateActivityStatusBreakdown(activities),
        reportSubmissionRate: this.calculateReportSubmissionRate(activities, reports),
        lateSubmissions: this.calculateLateSubmissions(reports),
        averageCompletionTime: this.calculateAverageCompletionTime(activities, reports),
        monthlyActivityTrends: this.calculateMonthlyActivityTrends(activities),
        projectActivityBreakdown: this.calculateProjectActivityBreakdown(activities),
      };

      // Process activities with report status
      const activitiesWithStatus = activities.map(a => ({
        id: a._id,
        title: a.title,
        description: a.description,
        budget: a.budget,
        status: a.status,
        dueDate: a.dueDate,
        project: a.project?.name,
        assignedTo: a.assignedTo?.fullName,
        createdAt: a.createdAt,
        hasReport: reports.some(r => r.activity._id.toString() === a._id.toString()),
        reportStatus: this.getActivityReportStatus(a, reports)
      }));

      return {
        analytics,
        activities: activitiesWithStatus
      };
    } catch (error) {
      throw error;
    }
  }

  // Generate budget analytics
  static async generateBudgetAnalytics(filters = {}) {
    try {
      const projects = await Project.find(filters.projectQuery || {})
        .populate('district', 'name');

      const totalBudget = projects.reduce((sum, p) => sum + (p.budget || 0), 0);
      const totalUsed = projects.reduce((sum, p) => sum + ((p.budget || 0) - (p.remainingBudget || 0)), 0);
      const totalRemaining = projects.reduce((sum, p) => sum + (p.remainingBudget || 0), 0);

      const analytics = {
        totalBudget,
        totalUsed,
        totalRemaining,
        utilizationRate: totalBudget > 0 ? (totalUsed / totalBudget) * 100 : 0,
        budgetByStatus: this.calculateBudgetByStatus(projects),
        budgetByDistrict: this.calculateBudgetByDistrict(projects),
        budgetByMonth: await this.calculateBudgetByMonth(),
        topSpendingProjects: this.getTopSpendingProjects(projects),
        budgetAlerts: this.generateBudgetAlerts(projects),
      };

      return analytics;
    } catch (error) {
      throw error;
    }
  }

  // Helper methods
  static calculateStatusBreakdown(projects) {
    const breakdown = {};
    projects.forEach(p => {
      breakdown[p.status] = (breakdown[p.status] || 0) + 1;
    });
    return breakdown;
  }

  static calculateSectorBreakdown(projects) {
    const breakdown = {};
    projects.forEach(p => {
      if (p.sector) {
        breakdown[p.sector] = (breakdown[p.sector] || 0) + 1;
      }
    });
    return breakdown;
  }

  static calculateDistrictBreakdown(projects) {
    const breakdown = {};
    projects.forEach(p => {
      const district = p.district?.name || 'Unknown';
      breakdown[district] = (breakdown[district] || 0) + 1;
    });
    return breakdown;
  }

  static async calculateMonthlyTrends(query) {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const trends = await Project.aggregate([
      {
        $match: {
          ...query,
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 },
          totalBudget: { $sum: "$budget" }
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1 }
      }
    ]);

    return trends.map(t => ({
      month: `${t._id.year}-${String(t._id.month).padStart(2, '0')}`,
      count: t.count,
      totalBudget: t.totalBudget
    }));
  }

  static calculateBudgetUtilization(projects) {
    return projects.map(p => ({
      projectName: p.name,
      budget: p.budget || 0,
      used: (p.budget || 0) - (p.remainingBudget || 0),
      remaining: p.remainingBudget || 0,
      utilizationRate: p.budget > 0 ? (((p.budget || 0) - (p.remainingBudget || 0)) / p.budget) * 100 : 0
    }));
  }

  static async calculateProjectProgress(projects) {
    const progressData = [];
    
    for (const project of projects) {
      const activities = await ProjectActivities.find({ project: project._id });
      const completedActivities = activities.filter(a => a.status === 'completed').length;
      const totalActivities = activities.length;
      const progress = totalActivities > 0 ? (completedActivities / totalActivities) * 100 : 0;
      
      progressData.push({
        projectName: project.name,
        totalActivities,
        completedActivities,
        progress: Math.round(progress)
      });
    }
    
    return progressData;
  }

  static async calculateSingleProjectProgress(projectId) {
    const activities = await ProjectActivities.find({ project: projectId });
    const completedActivities = activities.filter(a => a.status === 'completed').length;
    const totalActivities = activities.length;
    return totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;
  }

  static calculateActivityStatusBreakdown(activities) {
    const breakdown = {};
    activities.forEach(a => {
      breakdown[a.status] = (breakdown[a.status] || 0) + 1;
    });
    return breakdown;
  }

  static calculateReportSubmissionRate(activities, reports) {
    const activitiesWithReports = reports.length;
    const totalActivities = activities.length;
    return totalActivities > 0 ? (activitiesWithReports / totalActivities) * 100 : 0;
  }

  static calculateLateSubmissions(reports) {
    return reports.filter(r => r.status === 'lateSubmission').length;
  }

  static calculateAverageCompletionTime(activities, reports) {
    const completedActivities = activities.filter(a => a.status === 'completed');
    if (completedActivities.length === 0) return 0;

    let totalDays = 0;
    let count = 0;

    completedActivities.forEach(activity => {
      const report = reports.find(r => r.activity._id.toString() === activity._id.toString());
      if (report) {
        const daysTaken = Math.ceil((new Date(report.createdAt) - new Date(activity.createdAt)) / (1000 * 60 * 60 * 24));
        totalDays += daysTaken;
        count++;
      }
    });

    return count > 0 ? Math.round(totalDays / count) : 0;
  }

  static calculateMonthlyActivityTrends(activities) {
    const trends = {};
    activities.forEach(a => {
      const month = new Date(a.createdAt).toISOString().slice(0, 7);
      trends[month] = (trends[month] || 0) + 1;
    });
    return trends;
  }

  static calculateProjectActivityBreakdown(activities) {
    const breakdown = {};
    activities.forEach(a => {
      const projectName = a.project?.name || 'Unknown';
      breakdown[projectName] = (breakdown[projectName] || 0) + 1;
    });
    return breakdown;
  }

  static getActivityReportStatus(activity, reports) {
    const report = reports.find(r => r.activity._id.toString() === activity._id.toString());
    if (!report) {
      return new Date() > new Date(activity.dueDate) ? 'overdue' : 'pending';
    }
    return report.status;
  }

  static calculateBudgetByStatus(projects) {
    const breakdown = {};
    projects.forEach(p => {
      const used = (p.budget || 0) - (p.remainingBudget || 0);
      breakdown[p.status] = (breakdown[p.status] || 0) + used;
    });
    return breakdown;
  }

  static calculateBudgetByDistrict(projects) {
    const breakdown = {};
    projects.forEach(p => {
      const district = p.district?.name || 'Unknown';
      const used = (p.budget || 0) - (p.remainingBudget || 0);
      breakdown[district] = (breakdown[district] || 0) + used;
    });
    return breakdown;
  }

  static async calculateBudgetByMonth() {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const trends = await Project.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          totalBudget: { $sum: "$budget" },
          totalUsed: { $sum: { $subtract: ["$budget", "$remainingBudget"] } }
        }
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1 }
      }
    ]);

    return trends.map(t => ({
      month: `${t._id.year}-${String(t._id.month).padStart(2, '0')}`,
      totalBudget: t.totalBudget,
      totalUsed: t.totalUsed
    }));
  }

  static getTopSpendingProjects(projects, limit = 10) {
    return projects
      .map(p => ({
        name: p.name,
        budget: p.budget || 0,
        used: (p.budget || 0) - (p.remainingBudget || 0),
        utilizationRate: p.budget > 0 ? (((p.budget || 0) - (p.remainingBudget || 0)) / p.budget) * 100 : 0
      }))
      .sort((a, b) => b.used - a.used)
      .slice(0, limit);
  }

  static generateBudgetAlerts(projects) {
    const alerts = [];
    
    projects.forEach(p => {
      const used = (p.budget || 0) - (p.remainingBudget || 0);
      const utilizationRate = p.budget > 0 ? (used / p.budget) * 100 : 0;
      
      if (utilizationRate > 90) {
        alerts.push({
          type: 'high_utilization',
          projectName: p.name,
          utilizationRate: Math.round(utilizationRate),
          message: `Project "${p.name}" has used ${Math.round(utilizationRate)}% of its budget`
        });
      }
      
      if (p.remainingBudget < 0) {
        alerts.push({
          type: 'budget_exceeded',
          projectName: p.name,
          overspend: Math.abs(p.remainingBudget),
          message: `Project "${p.name}" has exceeded its budget by $${Math.abs(p.remainingBudget)}`
        });
      }
    });
    
    return alerts;
  }
}

module.exports = ReportingService;
