const express = require("express");
const {
  getAssignedActivities,
  getActiveProjects,
  getTeamForActivity,
  getPendingReports,
  getAllMyReports
} = require("../controllers/fieldOfficer.controller");
const { protect } = require("../middleware/auth");

const router = express.Router();

// Test route to verify field officer routes are working
router.get("/test", protect, (req, res) => {
  res.status(200).json({
    status: "success",
    message: "Field officer routes are working",
    user: req.user?.id,
    timestamp: new Date().toISOString()
  });
});

router.get("/activities/assigned", protect, getAssignedActivities);
router.get("/activities/active-projects", protect, getActiveProjects);
router.get("/activities/:id/team", protect, getTeamForActivity);
router.get("/reports/pending", protect, getPendingReports);
router.get("/reports/all", protect, getAllMyReports);

module.exports = router;