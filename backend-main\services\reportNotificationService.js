const nodemailer = require('nodemailer');

// Create email transporter (configure with your email service)
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.GMAIL_HOST || process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.GMAIL_PORT || process.env.SMTP_PORT || 465,
    secure: true, // Use SSL for Gmail
    auth: {
      user: process.env.GMAIL_EMAIL || process.env.SMTP_USER,
      pass: process.env.GMAIL_APP_PASSWORD || process.env.SMTP_PASS
    }
  });
};

/**
 * Send email notification to project manager when a report is submitted
 */
const sendReportSubmissionEmail = async (reportData) => {
  try {
    const { 
      projectManager, 
      fieldOfficer, 
      activity, 
      project, 
      reportId,
      submissionStatus 
    } = reportData;

    const transporter = createTransporter();

    const subject = `New Activity Report Submitted - ${activity.title}`;
    
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
          <h2 style="color: #2c3e50; margin-bottom: 20px;">📋 New Activity Report Submitted</h2>
          
          <div style="background-color: white; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
            <h3 style="color: #34495e; margin-top: 0;">Report Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Project:</td>
                <td style="padding: 8px 0;">${project.name}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Activity:</td>
                <td style="padding: 8px 0;">${activity.title}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Submitted By:</td>
                <td style="padding: 8px 0;">${fieldOfficer.fullName} (${fieldOfficer.email})</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Submission Status:</td>
                <td style="padding: 8px 0;">
                  <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; 
                    ${submissionStatus === 'onTime' ? 'background-color: #d4edda; color: #155724;' : 'background-color: #f8d7da; color: #721c24;'}">
                    ${submissionStatus === 'onTime' ? '✅ On Time' : '⚠️ Late Submission'}
                  </span>
                </td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Submitted On:</td>
                <td style="padding: 8px 0;">${new Date().toLocaleDateString()}</td>
              </tr>
            </table>
          </div>

          <div style="background-color: #e3f2fd; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
            <p style="margin: 0; color: #1565c0;">
              <strong>Action Required:</strong> Please review the submitted report and provide feedback or approval.
            </p>
          </div>

          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.FRONTEND_URL || 'http://10.136.10.162:3000'}/manager/reports"
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; 
                      border-radius: 6px; font-weight: bold; display: inline-block;">
              View Report Details
            </a>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; 
                      font-size: 12px; color: #6c757d; text-align: center;">
            <p>This is an automated notification from SPRODETA Project Management System.</p>
            <p>Please do not reply to this email.</p>
          </div>
        </div>
      </div>
    `;

    const textContent = `
New Activity Report Submitted

Project: ${project.name}
Activity: ${activity.title}
Submitted By: ${fieldOfficer.fullName} (${fieldOfficer.email})
Submission Status: ${submissionStatus === 'onTime' ? 'On Time' : 'Late Submission'}
Submitted On: ${new Date().toLocaleDateString()}

Please review the submitted report and provide feedback or approval.

View report details: ${process.env.FRONTEND_URL || 'http://10.136.10.162:3000'}/manager/reports

---
This is an automated notification from SPRODETA Project Management System.
    `;

    const mailOptions = {
      from: process.env.SMTP_FROM || '<EMAIL>',
      to: projectManager.email,
      subject: subject,
      text: textContent,
      html: htmlContent
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Report submission email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };

  } catch (error) {
    console.error('❌ Failed to send report submission email:', error);
    return { success: false, error: error.message };
  }
};

module.exports = {
  sendReportSubmissionEmail
};
