{"name": "m<PERSON>y", "version": "5.0.0", "description": "Expressive query building for MongoDB", "main": "lib/mquery.js", "scripts": {"test": "mocha --exit test/index.js test/*.test.js", "fix-lint": "eslint . --fix", "lint": "eslint ."}, "repository": {"type": "git", "url": "git://github.com/aheckmann/mquery.git"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"debug": "4.x"}, "devDependencies": {"eslint": "8.x", "eslint-plugin-mocha-no-only": "1.1.1", "mocha": "9.x", "mongodb": "5.x"}, "bugs": {"url": "https://github.com/aheckmann/mquery/issues/new"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["mongodb", "query", "builder"], "homepage": "https://github.com/aheckmann/mquery/"}