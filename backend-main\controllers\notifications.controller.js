const NotificationService = require("../services/notificationService");

// Get notifications for the current user
exports.getNotifications = async (req, res) => {
  try {
    console.log('🔔 Get notifications request received');
    console.log('👤 User:', req.user?.fullName || req.user?.email || 'Unknown');
    console.log('🆔 User ID:', req.user?._id || req.user?.id);

    const userId = req.user._id || req.user.id;
    const { page = 1, limit = 20, unreadOnly = false } = req.query;

    if (!userId) {
      console.error('❌ No user ID found in request');
      return res.status(401).json({
        status: "error",
        message: "User not authenticated",
      });
    }

    console.log('📋 Query params:', { page, limit, unreadOnly });

    const result = await NotificationService.getUserNotifications(
      userId,
      parseInt(page),
      parseInt(limit),
      unreadOnly === 'true'
    );

    console.log('✅ Notifications fetched:', result.notifications?.length || 0);

    res.status(200).json({
      status: "success",
      notifications: result.notifications,
      data: result.notifications,
      pagination: {
        page: result.page,
        totalPages: result.totalPages,
        total: result.total,
        unreadCount: result.unreadCount,
      },
    });
  } catch (error) {
    console.error("❌ Get notifications error:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch notifications",
      error: error.message
    });
  }
};

// Mark notification as read
exports.markAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    const notification = await NotificationService.markAsRead(notificationId, userId);

    if (!notification) {
      return res.status(404).json({
        status: "error",
        message: "Notification not found",
      });
    }

    res.status(200).json({
      status: "success",
      message: "Notification marked as read",
      data: notification,
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to mark notification as read",
    });
  }
};

// Mark all notifications as read
exports.markAllAsRead = async (req, res) => {
  try {
    const userId = req.user._id;

    await NotificationService.markAllAsRead(userId);

    res.status(200).json({
      status: "success",
      message: "All notifications marked as read",
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to mark all notifications as read",
    });
  }
};

// Get unread count
exports.getUnreadCount = async (req, res) => {
  try {
    const userId = req.user._id || req.user.id;

    const count = await NotificationService.getUnreadCount(userId);

    res.status(200).json({
      status: "success",
      data: {
        count: count,
      },
    });
  } catch (error) {
    console.error("Get unread count error:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to get unread count",
    });
  }
};





