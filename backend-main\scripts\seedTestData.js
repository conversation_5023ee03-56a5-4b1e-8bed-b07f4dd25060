const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/user.model');
const Role = require('../models/roles.model');
const Project = require('../models/project.model');
const ProjectActivities = require('../models/projectActivities.model');
const ActivityReport = require('../models/activityReport.model');

async function seedTestData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/project-management');
    console.log('Connected to MongoDB');

    // Clear existing data (optional - comment out if you want to keep existing data)
    // await User.deleteMany({});
    // await Role.deleteMany({});
    // await Project.deleteMany({});
    // await ProjectActivities.deleteMany({});
    // await ActivityReport.deleteMany({});

    // Create roles
    const roles = [
      { name: 'senior-manager', description: 'Senior Manager' },
      { name: 'project-manager', description: 'Project Manager' },
      { name: 'field-officer', description: 'Field Officer' },
      { name: 'accountant', description: 'Accountant' }
    ];

    const createdRoles = {};
    for (const roleData of roles) {
      let role = await Role.findOne({ name: roleData.name });
      if (!role) {
        role = await Role.create(roleData);
      }
      createdRoles[roleData.name] = role._id;
    }

    // Create test users
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    const users = [
      {
        fullName: 'John Senior Manager',
        email: '<EMAIL>',
        password: hashedPassword,
        role: createdRoles['senior-manager'],
        phoneNumber: '+************'
      },
      {
        fullName: 'Jane Project Manager',
        email: '<EMAIL>',
        password: hashedPassword,
        role: createdRoles['project-manager'],
        phoneNumber: '+************'
      },
      {
        fullName: 'Bob Field Officer',
        email: '<EMAIL>',
        password: hashedPassword,
        role: createdRoles['field-officer'],
        phoneNumber: '+************'
      },
      {
        fullName: 'Alice Accountant',
        email: '<EMAIL>',
        password: hashedPassword,
        role: createdRoles['accountant'],
        phoneNumber: '+************'
      }
    ];

    const createdUsers = {};
    for (const userData of users) {
      let user = await User.findOne({ email: userData.email });
      if (!user) {
        user = await User.create(userData);
      }
      createdUsers[userData.email] = user._id;
    }

    // Create test projects
    const projects = [
      {
        name: 'Water Supply Project',
        description: 'Providing clean water access to rural communities',
        budget: 500000,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        location: 'Lilongwe District',
        beneficiaries: 5000,
        objectives: ['Improve water access', 'Reduce waterborne diseases'],
        createdBy: createdUsers['<EMAIL>'],
        assignedTo: createdUsers['<EMAIL>'],
        status: 'active'
      },
      {
        name: 'Education Infrastructure',
        description: 'Building schools and improving education facilities',
        budget: 750000,
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-11-30'),
        location: 'Blantyre District',
        beneficiaries: 3000,
        objectives: ['Build new classrooms', 'Provide learning materials'],
        createdBy: createdUsers['<EMAIL>'],
        assignedTo: createdUsers['<EMAIL>'],
        status: 'active'
      },
      {
        name: 'Healthcare Initiative',
        description: 'Improving healthcare services in remote areas',
        budget: 300000,
        startDate: new Date('2024-03-01'),
        endDate: new Date('2024-10-31'),
        location: 'Mzuzu District',
        beneficiaries: 2000,
        objectives: ['Establish health posts', 'Train community health workers'],
        createdBy: createdUsers['<EMAIL>'],
        assignedTo: createdUsers['<EMAIL>'],
        status: 'pending'
      }
    ];

    const createdProjects = [];
    for (const projectData of projects) {
      let project = await Project.findOne({ name: projectData.name });
      if (!project) {
        project = await Project.create(projectData);
      }
      createdProjects.push(project);
    }

    // Create test activities
    const activities = [
      {
        project: createdProjects[0]._id,
        title: 'Drill Water Wells',
        description: 'Drilling 10 water wells in target communities',
        budget: 100000,
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-03-15'),
        location: 'Lilongwe Rural',
        expectedOutcome: '10 functional water wells',
        createdBy: createdUsers['<EMAIL>'],
        assignedTo: createdUsers['<EMAIL>'],
        status: 'completed'
      },
      {
        project: createdProjects[0]._id,
        title: 'Install Water Pumps',
        description: 'Installing solar-powered water pumps',
        budget: 80000,
        startDate: new Date('2024-03-16'),
        endDate: new Date('2024-05-16'),
        location: 'Lilongwe Rural',
        expectedOutcome: '10 solar pumps installed',
        createdBy: createdUsers['<EMAIL>'],
        assignedTo: createdUsers['<EMAIL>'],
        status: 'in-progress'
      },
      {
        project: createdProjects[1]._id,
        title: 'Build Classrooms',
        description: 'Constructing 5 new classrooms',
        budget: 200000,
        startDate: new Date('2024-02-15'),
        endDate: new Date('2024-06-15'),
        location: 'Blantyre Rural',
        expectedOutcome: '5 completed classrooms',
        createdBy: createdUsers['<EMAIL>'],
        assignedTo: createdUsers['<EMAIL>'],
        status: 'in-progress'
      }
    ];

    const createdActivities = [];
    for (const activityData of activities) {
      let activity = await ProjectActivities.findOne({ title: activityData.title });
      if (!activity) {
        activity = await ProjectActivities.create(activityData);
      }
      createdActivities.push(activity);
    }

    // Create test activity reports
    const reports = [
      {
        project: createdProjects[0]._id,
        activity: createdActivities[0]._id,
        submittedBy: createdUsers['<EMAIL>'],
        reportDate: new Date('2024-03-10'),
        location: 'Lilongwe Rural',
        description: 'Successfully completed drilling of 8 water wells. 2 wells encountered rocky terrain.',
        amountSpent: 85000,
        status: 'approved',
        approvedBy: createdUsers['<EMAIL>'],
        approvedAt: new Date('2024-03-12')
      },
      {
        project: createdProjects[0]._id,
        activity: createdActivities[1]._id,
        submittedBy: createdUsers['<EMAIL>'],
        reportDate: new Date('2024-04-15'),
        location: 'Lilongwe Rural',
        description: 'Installed 6 solar pumps. Waiting for additional equipment for remaining 4.',
        amountSpent: 45000,
        status: 'pending'
      },
      {
        project: createdProjects[1]._id,
        activity: createdActivities[2]._id,
        submittedBy: createdUsers['<EMAIL>'],
        reportDate: new Date('2024-04-20'),
        location: 'Blantyre Rural',
        description: 'Foundation work completed for 3 classrooms. Construction ongoing.',
        amountSpent: 120000,
        status: 'pending'
      }
    ];

    for (const reportData of reports) {
      let report = await ActivityReport.findOne({ 
        activity: reportData.activity,
        reportDate: reportData.reportDate 
      });
      if (!report) {
        await ActivityReport.create(reportData);
      }
    }

    console.log('✅ Test data seeded successfully!');
    console.log('📊 Created:');
    console.log(`   - ${roles.length} roles`);
    console.log(`   - ${users.length} users`);
    console.log(`   - ${projects.length} projects`);
    console.log(`   - ${activities.length} activities`);
    console.log(`   - ${reports.length} activity reports`);
    console.log('\n🔑 Test Login Credentials:');
    console.log('   Senior Manager: <EMAIL> / password123');
    console.log('   Project Manager: <EMAIL> / password123');
    console.log('   Field Officer: <EMAIL> / password123');
    console.log('   Accountant: <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Error seeding test data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the seeding function
seedTestData();
