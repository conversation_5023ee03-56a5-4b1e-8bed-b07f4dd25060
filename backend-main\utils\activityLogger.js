const Activity = require("../models/recentActivities.model");

/**
 * Log user activity
 * @param {Object} params - Activity parameters
 * @param {string} params.userId - User ID who performed the action
 * @param {string} params.action - Action type (from enum)
 * @param {string} params.description - Human readable description
 * @param {string} params.entityType - Type of entity (project, activity, user, etc.)
 * @param {string} params.entityId - ID of the entity
 * @param {string} params.entityName - Name of the entity
 * @param {Object} params.metadata - Additional metadata
 * @param {string} params.ipAddress - User's IP address
 * @param {string} params.userAgent - User's browser info
 */
const logActivity = async ({
  userId,
  action,
  description,
  entityType,
  entityId = null,
  entityName = null,
  metadata = {},
  ipAddress = null,
  userAgent = null
}) => {
  try {
    await Activity.create({
      user: userId,
      action,
      description,
      entityType,
      entityId,
      entityName,
      metadata,
      ipAddress,
      userAgent
    });
  } catch (error) {
    console.error("Failed to log activity:", error);
    // Don't throw error to avoid breaking main functionality
  }
};

/**
 * Helper functions for common activities
 */
const ActivityLogger = {
  // Project related activities
  projectCreated: (userId, projectId, projectName, req = null) => {
    return logActivity({
      userId,
      action: 'project_created',
      description: `Created project "${projectName}"`,
      entityType: 'project',
      entityId: projectId,
      entityName: projectName,
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  projectUpdated: (userId, projectId, projectName, changes, req = null) => {
    return logActivity({
      userId,
      action: 'project_updated',
      description: `Updated project "${projectName}"`,
      entityType: 'project',
      entityId: projectId,
      entityName: projectName,
      metadata: { changes },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  projectApproved: (userId, projectId, projectName, req = null) => {
    return logActivity({
      userId,
      action: 'project_approved',
      description: `Approved project "${projectName}"`,
      entityType: 'project',
      entityId: projectId,
      entityName: projectName,
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  projectRejected: (userId, projectId, projectName, reason, req = null) => {
    return logActivity({
      userId,
      action: 'project_rejected',
      description: `Rejected project "${projectName}"`,
      entityType: 'project',
      entityId: projectId,
      entityName: projectName,
      metadata: { reason },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  // Activity related activities
  activityCreated: (userId, activityId, activityTitle, projectName, req = null) => {
    return logActivity({
      userId,
      action: 'activity_created',
      description: `Created activity "${activityTitle}" in project "${projectName}"`,
      entityType: 'activity',
      entityId: activityId,
      entityName: activityTitle,
      metadata: { projectName },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  activityAssigned: (userId, activityId, activityTitle, assignedToName, req = null) => {
    return logActivity({
      userId,
      action: 'activity_assigned',
      description: `Assigned activity "${activityTitle}" to ${assignedToName}`,
      entityType: 'activity',
      entityId: activityId,
      entityName: activityTitle,
      metadata: { assignedTo: assignedToName },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  activityCompleted: (userId, activityId, activityTitle, req = null) => {
    return logActivity({
      userId,
      action: 'activity_completed',
      description: `Completed activity "${activityTitle}"`,
      entityType: 'activity',
      entityId: activityId,
      entityName: activityTitle,
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  activityReportSubmitted: (userId, activityId, activityTitle, req = null) => {
    return logActivity({
      userId,
      action: 'activity_report_submitted',
      description: `Submitted report for activity "${activityTitle}"`,
      entityType: 'report',
      entityId: activityId,
      entityName: activityTitle,
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  activityReportApproved: (userId, activityId, activityTitle, req = null) => {
    return logActivity({
      userId,
      action: 'activity_report_approved',
      description: `Approved report for activity "${activityTitle}"`,
      entityType: 'report',
      entityId: activityId,
      entityName: activityTitle,
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  // User activities
  userLogin: (userId, req = null) => {
    return logActivity({
      userId,
      action: 'user_login',
      description: 'Logged into the system',
      entityType: 'user',
      entityId: userId,
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  userLogout: (userId, req = null) => {
    return logActivity({
      userId,
      action: 'user_logout',
      description: 'Logged out of the system',
      entityType: 'user',
      entityId: userId,
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  // Admin activities
  userCreated: (adminId, newUserId, newUserName, newUserEmail, role, req = null) => {
    return logActivity({
      userId: adminId,
      action: 'user_created',
      description: `Created new user account for ${newUserName} (${newUserEmail}) with role ${role}`,
      entityType: 'user',
      entityId: newUserId,
      entityName: newUserName,
      metadata: { email: newUserEmail, role },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  userUpdated: (adminId, targetUserId, targetUserName, changes, req = null) => {
    return logActivity({
      userId: adminId,
      action: 'user_updated',
      description: `Updated user account for ${targetUserName}`,
      entityType: 'user',
      entityId: targetUserId,
      entityName: targetUserName,
      metadata: { changes },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  userDeleted: (adminId, targetUserId, targetUserName, targetUserEmail, req = null) => {
    return logActivity({
      userId: adminId,
      action: 'user_deleted',
      description: `Deleted user account for ${targetUserName} (${targetUserEmail})`,
      entityType: 'user',
      entityId: targetUserId,
      entityName: targetUserName,
      metadata: { email: targetUserEmail },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  userRoleChanged: (adminId, targetUserId, targetUserName, oldRole, newRole, req = null) => {
    return logActivity({
      userId: adminId,
      action: 'user_role_changed',
      description: `Changed role for ${targetUserName} from ${oldRole} to ${newRole}`,
      entityType: 'user',
      entityId: targetUserId,
      entityName: targetUserName,
      metadata: { oldRole, newRole },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  passwordReset: (adminId, targetUserId, targetUserName, req = null) => {
    return logActivity({
      userId: adminId,
      action: 'password_reset',
      description: `Reset password for user ${targetUserName}`,
      entityType: 'user',
      entityId: targetUserId,
      entityName: targetUserName,
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  // Status changes
  statusChanged: (userId, entityType, entityId, entityName, oldStatus, newStatus, req = null) => {
    return logActivity({
      userId,
      action: 'status_changed',
      description: `Changed ${entityType} "${entityName}" status from ${oldStatus} to ${newStatus}`,
      entityType,
      entityId,
      entityName,
      metadata: { oldStatus, newStatus },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  // Budget updates
  budgetUpdated: (userId, projectId, projectName, oldBudget, newBudget, req = null) => {
    return logActivity({
      userId,
      action: 'budget_updated',
      description: `Updated budget for project "${projectName}" from MK ${oldBudget?.toLocaleString()} to MK ${newBudget?.toLocaleString()}`,
      entityType: 'project',
      entityId: projectId,
      entityName: projectName,
      metadata: { oldBudget, newBudget },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  },

  // Team assignment
  teamAssigned: (adminId, teamId, projectManagerId, req = null) => {
    return logActivity({
      userId: adminId,
      action: 'team_assigned',
      description: `Assigned team to project manager`,
      entityType: 'team',
      entityId: teamId,
      metadata: { assignedTo: projectManagerId },
      ipAddress: req?.ip,
      userAgent: req?.get('User-Agent')
    });
  }
};

module.exports = ActivityLogger;
