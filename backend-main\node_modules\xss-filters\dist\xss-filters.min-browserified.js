/**
 * xss-filters - v1.2.7
 * Yahoo! Inc. Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.
 */
!function(a){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=a();else if("function"==typeof define&&define.amd)define([],a);else{var b;b="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,b.xssFilters=a()}}(function(){return function a(b,c,d){function e(g,h){if(!c[g]){if(!b[g]){var i="function"==typeof require&&require;if(!h&&i)return i(g,!0);if(f)return f(g,!0);var j=new Error("Cannot find module '"+g+"'");throw j.code="MODULE_NOT_FOUND",j}var k=c[g]={exports:{}};b[g][0].call(k.exports,function(a){var c=b[g][1][a];return e(c?c:a)},k,k.exports,a,b,c,d)}return c[g].exports}for(var f="function"==typeof require&&require,g=0;g<d.length;g++)e(d[g]);return e}({1:[function(a,b,c){function d(a,b,c){return e.yubl(b((c||e.yufull)(a)))}c._getPrivFilters=function(){function a(a){var b=a.split(x,2);return!b[0]||2!==b.length&&a.length===b[0].length?null:b[0]}function b(a,b,c,d){function e(a,c,e,g){return c?(c=Number(c[0]<="9"?c:"0"+c),d?B(c):128===c?"€":130===c?"‚":131===c?"ƒ":132===c?"„":133===c?"…":134===c?"†":135===c?"‡":136===c?"ˆ":137===c?"‰":138===c?"Š":139===c?"‹":140===c?"Œ":142===c?"Ž":145===c?"‘":146===c?"’":147===c?"“":148===c?"”":149===c?"•":150===c?"–":151===c?"—":152===c?"˜":153===c?"™":154===c?"š":155===c?"›":156===c?"œ":158===c?"ž":159===c?"Ÿ":c>=55296&&c<=57343||13===c?"�":f.frCoPt(c)):b[e||g]||a}return b=b||p,c=c||o,void 0===a?"undefined":null===a?"null":a.toString().replace(k,"�").replace(c,e)}function c(a){return"\\"+a.charCodeAt(0).toString(16).toLowerCase()+" "}function d(a){return a.replace(t,function(a){return"-x-"+a})}function e(c){c=f.yufull(b(c));var d=a(c);return d&&w[d.toLowerCase()]?"##"+c:c}var f,g=/</g,h=/"/g,i=/'/g,j=/&/g,k=/\x00/g,l=/(?:^$|[\x00\x09-\x0D "'`=<>])/g,m=/[&<>"'`]/g,n=/(?:\x00|^-*!?>|--!?>|--?!?$|\]>|\]$)/g,o=/&(?:#([xX][0-9A-Fa-f]+|\d+);?|(Tab|NewLine|colon|semi|lpar|rpar|apos|sol|comma|excl|ast|midast|ensp|emsp|thinsp);|(nbsp|amp|AMP|lt|LT|gt|GT|quot|QUOT);?)/g,p={Tab:"\t",NewLine:"\n",colon:":",semi:";",lpar:"(",rpar:")",apos:"'",sol:"/",comma:",",excl:"!",ast:"*",midast:"*",ensp:" ",emsp:" ",thinsp:" ",nbsp:" ",amp:"&",lt:"<",gt:">",quot:'"',QUOT:'"'},q=/^(?:(?!-*expression)#?[-\w]+|[+-]?(?:\d+|\d*\.\d+)(?:r?em|ex|ch|cm|mm|in|px|pt|pc|%|vh|vw|vmin|vmax)?|!important|)$/i,r=/[\x00-\x1F\x7F\[\]{}\\"]/g,s=/[\x00-\x1F\x7F\[\]{}\\']/g,t=/url[\(\u207D\u208D]+/g,u=/['\(\)]/g,v=/\/\/%5[Bb]([A-Fa-f0-9:]+)%5[Dd]/,w={javascript:1,data:1,vbscript:1,mhtml:1,"x-schema":1},x=/(?::|&#[xX]0*3[aA];?|&#0*58;?|&colon;)/,y=/(?:^[\x00-\x20]+|[\t\n\r\x00]+)/g,z={Tab:"\t",NewLine:"\n"},A=function(a,b,c){return void 0===a?"undefined":null===a?"null":a.toString().replace(b,c)},B=String.fromCodePoint||function(a){return 0===arguments.length?"":a<=65535?String.fromCharCode(a):(a-=65536,String.fromCharCode((a>>10)+55296,a%1024+56320))};return f={frCoPt:function(a){return void 0===a||null===a?"":!isFinite(a=Number(a))||a<=0||a>1114111||a>=1&&a<=8||a>=14&&a<=31||a>=127&&a<=159||a>=64976&&a<=65007||11===a||65535===(65535&a)||65534===(65535&a)?"�":B(a)},d:b,yup:function(c){return c=a(c.replace(k,"")),c?b(c,z,null,!0).replace(y,"").toLowerCase():null},y:function(a){return A(a,m,function(a){return"&"===a?"&amp;":"<"===a?"&lt;":">"===a?"&gt;":'"'===a?"&quot;":"'"===a?"&#39;":"&#96;"})},ya:function(a){return A(a,j,"&amp;")},yd:function(a){return A(a,g,"&lt;")},yc:function(a){return A(a,n,function(a){return"\0"===a?"�":"--!"===a||"--"===a||"-"===a||"]"===a?a+" ":a.slice(0,-1)+" >"})},yavd:function(a){return A(a,h,"&quot;")},yavs:function(a){return A(a,i,"&#39;")},yavu:function(a){return A(a,l,function(a){return"\t"===a?"&#9;":"\n"===a?"&#10;":"\x0B"===a?"&#11;":"\f"===a?"&#12;":"\r"===a?"&#13;":" "===a?"&#32;":"="===a?"&#61;":"<"===a?"&lt;":">"===a?"&gt;":'"'===a?"&quot;":"'"===a?"&#39;":"`"===a?"&#96;":"�"})},yu:encodeURI,yuc:encodeURIComponent,yubl:function(a){return w[f.yup(a)]?"x-"+a:a},yufull:function(a){return f.yu(a).replace(v,function(a,b){return"//["+b+"]"})},yublf:function(a){return f.yubl(f.yufull(a))},yceu:function(a){return a=b(a),q.test(a)?a:";-x:'"+d(a.replace(s,c))+"';-v:"},yced:function(a){return d(b(a).replace(r,c))},yces:function(a){return d(b(a).replace(s,c))},yceuu:function(a){return e(a).replace(u,function(a){return"'"===a?"\\27 ":"("===a?"%28":"%29"})},yceud:function(a){return e(a)},yceus:function(a){return e(a).replace(i,"\\27 ")}}};var e=c._privFilters=c._getPrivFilters();c.inHTMLData=e.yd,c.inHTMLComment=e.yc,c.inSingleQuotedAttr=e.yavs,c.inDoubleQuotedAttr=e.yavd,c.inUnQuotedAttr=e.yavu,c.uriInSingleQuotedAttr=function(a){return d(a,e.yavs)},c.uriInDoubleQuotedAttr=function(a){return d(a,e.yavd)},c.uriInUnQuotedAttr=function(a){return d(a,e.yavu)},c.uriInHTMLData=e.yufull,c.uriInHTMLComment=function(a){return e.yc(e.yufull(a))},c.uriPathInSingleQuotedAttr=function(a){return d(a,e.yavs,e.yu)},c.uriPathInDoubleQuotedAttr=function(a){return d(a,e.yavd,e.yu)},c.uriPathInUnQuotedAttr=function(a){return d(a,e.yavu,e.yu)},c.uriPathInHTMLData=e.yu,c.uriPathInHTMLComment=function(a){return e.yc(e.yu(a))},c.uriQueryInSingleQuotedAttr=c.uriPathInSingleQuotedAttr,c.uriQueryInDoubleQuotedAttr=c.uriPathInDoubleQuotedAttr,c.uriQueryInUnQuotedAttr=c.uriPathInUnQuotedAttr,c.uriQueryInHTMLData=c.uriPathInHTMLData,c.uriQueryInHTMLComment=c.uriPathInHTMLComment,c.uriComponentInSingleQuotedAttr=function(a){return e.yavs(e.yuc(a))},c.uriComponentInDoubleQuotedAttr=function(a){return e.yavd(e.yuc(a))},c.uriComponentInUnQuotedAttr=function(a){return e.yavu(e.yuc(a))},c.uriComponentInHTMLData=e.yuc,c.uriComponentInHTMLComment=function(a){return e.yc(e.yuc(a))},c.uriFragmentInSingleQuotedAttr=function(a){return e.yubl(e.yavs(e.yuc(a)))},c.uriFragmentInDoubleQuotedAttr=function(a){return e.yubl(e.yavd(e.yuc(a)))},c.uriFragmentInUnQuotedAttr=function(a){return e.yubl(e.yavu(e.yuc(a)))},c.uriFragmentInHTMLData=c.uriComponentInHTMLData,c.uriFragmentInHTMLComment=c.uriComponentInHTMLComment},{}]},{},[1])(1)});