const jwt = require("jsonwebtoken");
const User = require("../models/user.model");

// Protect routes
exports.protect = async (req, res, next) => {
  let token;

  // Get token from cookie or Authorization header
  if (req.cookies && req.cookies.token) {
    token = req.cookies.token;
  } else if (req.headers.authorization && req.headers.authorization.startsWith("Bearer")) {
    token = req.headers.authorization.split(" ")[1];
  }

  // Check if token exists
  if (!token) {
    console.error("🔐 No token found in request");
    return res.status(401).json({
      status: "failed",
      message: "Not authorized to access this route"
    });
  }

  console.log("🔐 Token found, length:", token.length, "First 20 chars:", token.substring(0, 20) + "...");

  try {
    // Verify token
    console.log("🔐 Attempting to verify token with JWT_SECRET:", process.env.JWT_SECRET ? "Present" : "Missing");
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log("🔐 Token decoded successfully:", { userId: decoded.id, role: decoded.role });

    // Get user from token with role populated
    const user = await User.findById(decoded.id).select("-password").populate("role", "name");

    if (!user) {
      console.error("🔐 User not found for token:", decoded.id);
      return res.status(401).json({
        status: "failed",
        message: "User not found"
      });
    }

    console.log("🔐 User found:", {
      id: user._id,
      email: user.email,
      role: user.role?.name || user.role || 'No role',
      fullName: user.fullName,
      roleObject: user.role
    });

    // Update user's online status and last seen timestamp on each authenticated request
    await User.findByIdAndUpdate(user._id, {
      isOnline: true,
      lastSeen: new Date()
    });

    req.user = user;
    next();
  } catch (error) {
    console.error("🔐 JWT verification error:", error.message);
    return res.status(401).json({
      status: "failed",
      message: "Not authorized to access this route"
    });
  }
};

// Grant access to specific roles
exports.authorize = (...roles) => {
  return (req, res, next) => {
    console.log("🔐 Authorization check - Required roles:", roles);

    if (!req.user) {
      console.error("🔐 Authorization failed - No user in request");
      return res.status(401).json({
        status: "failed",
        message: "Not authorized to access this route"
      });
    }

    // Check if user has a role
    if (!req.user.role) {
      console.error("🔐 Authorization failed - User has no role:", req.user.email);
      return res.status(403).json({
        status: "failed",
        message: "User has no role assigned"
      });
    }

    // Get role name - handle both populated and non-populated cases
    const userRoleName = req.user.role.name || req.user.role;
    console.log("🔐 User role:", userRoleName, "Required roles:", roles);

    if (!roles.includes(userRoleName)) {
      console.error("🔐 Authorization failed - Role not allowed:", {
        userRole: userRoleName,
        requiredRoles: roles,
        userEmail: req.user.email
      });
      return res.status(403).json({
        status: "failed",
        message: `User role ${userRoleName} is not authorized to access this route`
      });
    }

    console.log("✅ Authorization successful for user:", req.user.email, "with role:", userRoleName);
    next();
  };
};