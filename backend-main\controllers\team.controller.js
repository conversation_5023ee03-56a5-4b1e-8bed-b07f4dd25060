const mongoose = require("mongoose");
const Team = require("../models/team.model");
const User = require("../models/user.model");
const Role = require("../models/roles.model");

// Create a new team (Admin only - original functionality)
exports.createTeam = async (req, res) => {
  try {
    const { name, description, projectManager, fieldOfficers } = req.body;
    const createdBy = req.user.id;

    console.log('🏗️ Creating team with data:', { name, description, projectManager, fieldOfficers, createdBy });

    // Validate required fields
    if (!name || !projectManager || !fieldOfficers || fieldOfficers.length === 0) {
      return res.status(400).json({
        status: "failed",
        message: "Please provide team name, project manager, and at least one field officer"
      });
    }

    // Verify project manager exists and has correct role
    const pmUser = await User.findById(projectManager).populate('role');
    if (!pmUser) {
      return res.status(404).json({
        status: "failed",
        message: "Project manager not found"
      });
    }

    if (pmUser.role.name !== 'projectManager') {
      return res.status(400).json({
        status: "failed",
        message: "Selected user is not a project manager"
      });
    }

    // Verify all field officers exist and have correct role
    const officers = await User.find({ 
      _id: { $in: fieldOfficers },
      isActive: { $ne: false }
    }).populate('role');

    if (officers.length !== fieldOfficers.length) {
      return res.status(400).json({
        status: "failed",
        message: "One or more field officers not found or inactive"
      });
    }

    const invalidOfficers = officers.filter(officer => officer.role.name !== 'fieldOfficer');
    if (invalidOfficers.length > 0) {
      return res.status(400).json({
        status: "failed",
        message: "One or more selected users are not field officers"
      });
    }

    // Check if team name already exists
    const existingTeam = await Team.findOne({ name: name.trim(), isActive: true });
    if (existingTeam) {
      return res.status(400).json({
        status: "failed",
        message: "A team with this name already exists"
      });
    }

    // Create the team
    const team = await Team.create({
      name: name.trim(),
      description: description?.trim() || "",
      projectManager,
      fieldOfficers,
      createdBy
    });

    // Populate the created team for response
    const populatedTeam = await Team.findById(team._id)
      .populate('projectManager', 'fullName email district')
      .populate({
        path: 'fieldOfficers',
        select: 'fullName email phoneNumber district',
        populate: {
          path: 'district',
          select: 'name'
        }
      })
      .populate({
        path: 'projectManager',
        populate: {
          path: 'district',
          select: 'name'
        }
      })
      .populate('createdBy', 'fullName email');

    console.log('✅ Team created successfully:', populatedTeam);

    res.status(201).json({
      status: "success",
      message: "Team created successfully",
      data: populatedTeam
    });

  } catch (error) {
    console.error('❌ Error creating team:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while creating the team. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all teams
exports.getAllTeams = async (req, res) => {
  try {
    const { page = 1, limit = 10, search, projectManager, isActive } = req.query;

    console.log('📋 getAllTeams called with params:', { page, limit, search, projectManager, isActive });

    // Check if there are any teams at all
    const totalTeamsCount = await Team.countDocuments();
    console.log('📊 Total teams in database:', totalTeamsCount);

    // Build filter object - default to showing active teams
    let filter = { isActive: isActive !== 'false' }; // Show active teams unless explicitly set to false

    console.log('📋 Filter applied:', filter);
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (projectManager) {
      filter.projectManager = projectManager;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Get teams with pagination
    const teams = await Team.find(filter)
      .populate({
        path: 'projectManager',
        select: 'fullName email district',
        populate: {
          path: 'district',
          select: 'name'
        }
      })
      .populate({
        path: 'fieldOfficers',
        select: 'fullName email phoneNumber district',
        populate: {
          path: 'district',
          select: 'name'
        }
      })
      .populate('createdBy', 'fullName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Team.countDocuments(filter);

    console.log(`📋 Retrieved ${teams.length} teams out of ${total} total`);
    console.log('📋 Teams found:', teams.map(t => ({ id: t._id, name: t.name, isActive: t.isActive })));

    res.status(200).json({
      status: "success",
      data: teams,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('❌ Error fetching teams:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching teams. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get team by ID
exports.getTeamById = async (req, res) => {
  try {
    const { id } = req.params;

    const team = await Team.findById(id)
      .populate('projectManager', 'fullName email phoneNumber district')
      .populate('fieldOfficers', 'fullName email phoneNumber district')
      .populate('createdBy', 'fullName email');

    if (!team) {
      return res.status(404).json({
        status: "failed",
        message: "Team not found"
      });
    }

    res.status(200).json({
      status: "success",
      data: team
    });

  } catch (error) {
    console.error('❌ Error fetching team:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching the team. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update team
exports.updateTeam = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, projectManager, fieldOfficers } = req.body;

    console.log('🔄 Updating team:', id, 'with data:', { name, description, projectManager, fieldOfficers });

    // Validate ObjectId format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      console.log('❌ Invalid team ID format:', id);
      return res.status(400).json({
        status: "failed",
        message: "Invalid team ID format"
      });
    }

    if (projectManager && !mongoose.Types.ObjectId.isValid(projectManager)) {
      console.log('❌ Invalid project manager ID format:', projectManager);
      return res.status(400).json({
        status: "failed",
        message: "Invalid project manager ID format"
      });
    }

    if (fieldOfficers && Array.isArray(fieldOfficers)) {
      const invalidOfficerIds = fieldOfficers.filter(id => !mongoose.Types.ObjectId.isValid(id));
      if (invalidOfficerIds.length > 0) {
        console.log('❌ Invalid field officer ID formats:', invalidOfficerIds);
        return res.status(400).json({
          status: "failed",
          message: `Invalid field officer ID formats: ${invalidOfficerIds.join(', ')}`
        });
      }
    }

    console.log('🔍 Looking for team with ID:', id);
    const team = await Team.findById(id);
    if (!team) {
      console.log('❌ Team not found with ID:', id);
      return res.status(404).json({
        status: "failed",
        message: "Team not found"
      });
    }

    console.log('✅ Found team:', {
      id: team._id,
      name: team.name,
      currentPM: team.projectManager,
      currentOfficers: team.fieldOfficers
    });

    // Validate project manager if provided
    if (projectManager && projectManager !== team.projectManager.toString()) {
      console.log('🔍 Validating project manager:', projectManager);
      const pmUser = await User.findById(projectManager).populate('role');
      console.log('👤 Found PM user:', pmUser ? { id: pmUser._id, name: pmUser.fullName, role: pmUser.role?.name } : 'Not found');

      if (!pmUser) {
        console.log('❌ Project manager not found');
        return res.status(400).json({
          status: "failed",
          message: "Project manager not found"
        });
      }

      if (pmUser.role?.name !== 'projectManager') {
        console.log('❌ User is not a project manager, role:', pmUser.role?.name);
        return res.status(400).json({
          status: "failed",
          message: `Selected user is not a project manager. Current role: ${pmUser.role?.name}`
        });
      }
    }

    // Validate field officers if provided
    if (fieldOfficers && fieldOfficers.length > 0) {
      console.log('🔍 Validating field officers:', fieldOfficers);
      const officers = await User.find({
        _id: { $in: fieldOfficers },
        isActive: { $ne: false }
      }).populate('role');

      console.log('👥 Found officers:', officers.map(o => ({ id: o._id, name: o.fullName, role: o.role?.name })));

      if (officers.length !== fieldOfficers.length) {
        console.log('❌ Officer count mismatch - requested:', fieldOfficers.length, 'found:', officers.length);
        const foundIds = officers.map(o => o._id.toString());
        const missingIds = fieldOfficers.filter(id => !foundIds.includes(id));
        console.log('❌ Missing officer IDs:', missingIds);

        return res.status(400).json({
          status: "failed",
          message: `One or more field officers not found or inactive. Missing IDs: ${missingIds.join(', ')}`
        });
      }

      const invalidOfficers = officers.filter(officer => officer.role?.name !== 'fieldOfficer');
      if (invalidOfficers.length > 0) {
        console.log('❌ Invalid officers (not fieldOfficer role):', invalidOfficers.map(o => ({ id: o._id, name: o.fullName, role: o.role?.name })));
        return res.status(400).json({
          status: "failed",
          message: `One or more selected users are not field officers: ${invalidOfficers.map(o => o.fullName).join(', ')}`
        });
      }
    }

    // Check if new name conflicts with existing teams
    if (name && name.trim() !== team.name) {
      const existingTeam = await Team.findOne({ 
        name: name.trim(), 
        isActive: true,
        _id: { $ne: id }
      });
      if (existingTeam) {
        return res.status(400).json({
          status: "failed",
          message: "A team with this name already exists"
        });
      }
    }

    // Update team
    const updatedTeam = await Team.findByIdAndUpdate(
      id,
      {
        ...(name && { name: name.trim() }),
        ...(description !== undefined && { description: description.trim() }),
        ...(projectManager && { projectManager }),
        ...(fieldOfficers && { fieldOfficers })
      },
      { new: true, runValidators: true }
    )
      .populate('projectManager', 'fullName email')
      .populate('fieldOfficers', 'fullName email phoneNumber district')
      .populate('createdBy', 'fullName email');

    console.log('✅ Team updated successfully:', updatedTeam);

    res.status(200).json({
      status: "success",
      message: "Team updated successfully",
      data: updatedTeam
    });

  } catch (error) {
    console.error('❌ Error updating team:', error);
    console.error('❌ Error stack:', error.stack);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while updating the team. Please try again.",
      error: process.env.NODE_ENV === 'development' ? {
        message: error.message,
        stack: error.stack
      } : undefined
    });
  }
};

// Delete team (soft delete)
exports.deleteTeam = async (req, res) => {
  try {
    const { id } = req.params;

    const team = await Team.findById(id);
    if (!team) {
      return res.status(404).json({
        status: "failed",
        message: "Team not found"
      });
    }

    // Soft delete by setting isActive to false
    await Team.findByIdAndUpdate(id, { isActive: false });

    console.log('🗑️ Team soft deleted:', id);

    res.status(200).json({
      status: "success",
      message: "Team deleted successfully"
    });

  } catch (error) {
    console.error('❌ Error deleting team:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while deleting the team. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Assign team to project manager
exports.assignTeamToManager = async (req, res) => {
  try {
    const { teamId } = req.params;
    const { projectManagerId, message } = req.body;

    console.log('🎯 Starting team assignment:', { teamId, projectManagerId, message });

    // Validate required fields
    if (!projectManagerId) {
      console.log('❌ Missing project manager ID');
      return res.status(400).json({
        status: "failed",
        message: "Please provide project manager ID"
      });
    }

    console.log('🔍 Looking for team...');
    // Check if team exists
    const team = await Team.findById(teamId);
    if (!team) {
      console.log('❌ Team not found');
      return res.status(404).json({
        status: "failed",
        message: "Team not found"
      });
    }
    console.log('✅ Team found:', team.name);

    console.log('🔍 Looking for project manager...');
    // Check if project manager exists and has correct role
    const projectManager = await User.findById(projectManagerId).populate('role');
    if (!projectManager) {
      console.log('❌ Project manager not found');
      return res.status(404).json({
        status: "failed",
        message: "Project manager not found"
      });
    }
    console.log('✅ Project manager found:', projectManager.fullName, 'Role:', projectManager.role?.name);

    if (projectManager.role?.name !== 'projectManager') {
      console.log('❌ User is not a project manager, role:', projectManager.role?.name);
      return res.status(400).json({
        status: "failed",
        message: "Selected user is not a project manager"
      });
    }

    // Update team assignment
    console.log('📝 Updating team assignment...');
    team.assignedTo = projectManagerId;
    team.assignedAt = new Date();
    team.assignmentMessage = message || `Team "${team.name}" has been assigned to you`;

    console.log('💾 Saving team with assignment data...');
    await team.save();
    console.log('✅ Team saved successfully');

    // Create notification for project manager
    try {
      console.log('📧 Creating notification for project manager:', {
        recipient: projectManagerId,
        type: 'team_assigned',
        title: 'New Team Assigned',
        message: team.assignmentMessage
      });

      const NotificationService = require('../services/notificationService');
      const notification = await NotificationService.createNotification({
        recipient: projectManagerId,
        type: 'team_assigned',
        title: 'New Team Assigned',
        message: team.assignmentMessage,
        relatedEntity: {
          type: 'team',
          id: team._id,
          name: team.name
        }
      });
      console.log('✅ Notification created successfully:', notification._id);
    } catch (notificationError) {
      console.error('❌ Failed to create notification:', notificationError);
      console.error('❌ Notification error details:', notificationError.message);
      console.error('❌ Notification error stack:', notificationError.stack);
      // Don't fail the whole operation if notification fails
    }

    // Log the assignment activity
    try {
      const ActivityLogger = require('../utils/activityLogger');
      await ActivityLogger.teamAssigned(req.user.id, team._id, projectManager._id, req);
      console.log('✅ Activity logged successfully');
    } catch (activityError) {
      console.error('❌ Failed to log activity:', activityError);
      // Don't fail the whole operation if activity logging fails
    }

    console.log('📤 Sending success response...');
    res.status(200).json({
      status: "success",
      message: "Team assigned successfully",
      data: {
        teamId: team._id,
        teamName: team.name,
        assignedTo: projectManager.fullName,
        assignedAt: team.assignedAt,
        message: team.assignmentMessage
      }
    });
    console.log('✅ Response sent successfully');

  } catch (error) {
    console.error('❌ Error assigning team:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while assigning the team. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// ========== PROJECT MANAGER TEAM MANAGEMENT ==========

// Get projects assigned to project manager for team creation
exports.getProjectsForTeamCreation = async (req, res) => {
  try {
    const projectManagerId = req.user.id;
    console.log('🔍 Getting projects for team creation for PM:', projectManagerId);
    console.log('🔍 PM ID type:', typeof projectManagerId);

    const Project = require('../models/project.model');
    const mongoose = require('mongoose');

    // First, let's check all projects in the database for debugging
    const allProjects = await Project.find({})
      .populate('location', 'name')
      .populate('assignedTo', 'fullName email')
      .select('title description location startDate endDate status assignedTo');

    console.log('🔍 Total projects in database:', allProjects.length);
    console.log('🔍 All projects with assignment info:', allProjects.map(p => ({
      id: p._id,
      title: p.title,
      status: p.status,
      assignedTo: p.assignedTo ? {
        id: p.assignedTo._id,
        name: p.assignedTo.fullName,
        email: p.assignedTo.email
      } : null,
      assignedToRaw: p.assignedTo
    })));

    // Try multiple query approaches to find the issue
    console.log('🔍 Trying different query approaches...');

    // Approach 1: Direct string comparison
    const projectsString = await Project.find({
      assignedTo: projectManagerId,
      status: 'inprogress'
    })
    .populate('location', 'name')
    .select('title description location startDate endDate');

    console.log('🔍 Approach 1 (string comparison) found:', projectsString.length, 'projects');

    // Approach 2: ObjectId comparison
    const projectsObjectId = await Project.find({
      assignedTo: new mongoose.Types.ObjectId(projectManagerId),
      status: 'inprogress'
    })
    .populate('location', 'name')
    .select('title description location startDate endDate');

    console.log('🔍 Approach 2 (ObjectId comparison) found:', projectsObjectId.length, 'projects');

    // Approach 3: Without status filter
    const projectsNoStatus = await Project.find({
      assignedTo: projectManagerId
    })
    .populate('location', 'name')
    .select('title description location startDate endDate status');

    console.log('🔍 Approach 3 (no status filter) found:', projectsNoStatus.length, 'projects');
    if (projectsNoStatus.length > 0) {
      console.log('🔍 Projects without status filter:', projectsNoStatus.map(p => ({
        title: p.title,
        status: p.status
      })));
    }

    // Use the approach that returns results, prioritizing the correct one
    let projects = projectsString.length > 0 ? projectsString :
                   projectsObjectId.length > 0 ? projectsObjectId :
                   projectsNoStatus.filter(p => p.status === 'inprogress');

    console.log('✅ Final projects for team creation:', projects.length);

    res.status(200).json({
      status: "success",
      data: projects
    });

  } catch (error) {
    console.error('❌ Error getting projects for team creation:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching projects. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get field officers in the same location as a project
exports.getFieldOfficersByProjectLocation = async (req, res) => {
  try {
    const { projectId } = req.params;
    const projectManagerId = req.user.id;

    console.log('🔍 Getting field officers for project:', projectId);
    console.log('🔍 Project Manager ID:', projectManagerId);

    // Validate projectId format
    if (!mongoose.Types.ObjectId.isValid(projectId)) {
      console.log('❌ Invalid project ID format:', projectId);
      return res.status(400).json({
        status: "failed",
        message: "Invalid project ID format"
      });
    }

    const Project = require('../models/project.model');

    // First, let's check if the project exists at all
    const projectExists = await Project.findById(projectId).populate('location', 'name').populate('assignedTo', 'fullName email');
    console.log('🔍 Project exists check:', {
      exists: !!projectExists,
      title: projectExists?.title,
      location: projectExists?.location?.name,
      assignedTo: projectExists?.assignedTo ? {
        id: projectExists.assignedTo._id.toString(),
        name: projectExists.assignedTo.fullName
      } : null
    });

    if (!projectExists) {
      return res.status(404).json({
        status: "failed",
        message: "Project not found"
      });
    }

    // Check if project is assigned to this project manager
    const isAssignedToUser = projectExists.assignedTo &&
      (projectExists.assignedTo._id.toString() === projectManagerId ||
       projectExists.assignedTo._id === projectManagerId);

    console.log('🔍 Assignment check:', {
      projectAssignedTo: projectExists.assignedTo?._id?.toString(),
      currentUserId: projectManagerId,
      isAssigned: isAssignedToUser
    });

    if (!isAssignedToUser) {
      return res.status(403).json({
        status: "failed",
        message: "Project is not assigned to you",
        debug: {
          projectAssignedTo: projectExists.assignedTo?._id?.toString(),
          currentUserId: projectManagerId
        }
      });
    }

    const project = projectExists;
    console.log('✅ Project found:', project.title, 'Location:', project.location?.name);

    // Check if project has a location
    if (!project.location || !project.location._id) {
      return res.status(400).json({
        status: "failed",
        message: "Project does not have a valid location assigned"
      });
    }

    // Get field officers in the same location as the project
    const fieldOfficerRole = await Role.findOne({ name: 'fieldOfficer' });
    console.log('🔍 Field officer role found:', !!fieldOfficerRole);

    if (!fieldOfficerRole) {
      return res.status(404).json({
        status: "failed",
        message: "Field officer role not found"
      });
    }

    // Query for field officers
    console.log('🔍 Searching for field officers with:', {
      role: fieldOfficerRole._id,
      district: project.location._id,
      locationName: project.location.name
    });

    const fieldOfficers = await User.find({
      role: fieldOfficerRole._id,
      district: project.location._id,
      isActive: { $ne: false }
    })
    .populate('district', 'name')
    .select('fullName email phoneNumber district');

    console.log('✅ Found field officers in location:', fieldOfficers.length);
    console.log('🔍 Field officers details:', fieldOfficers.map(fo => ({
      name: fo.fullName,
      email: fo.email,
      district: fo.district?.name
    })));

    res.status(200).json({
      status: "success",
      data: {
        project: {
          _id: project._id,
          title: project.title,
          location: project.location
        },
        fieldOfficers
      }
    });

  } catch (error) {
    console.error('❌ Error getting field officers by project location:', error);
    console.error('❌ Error stack:', error.stack);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching field officers. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Create team for a specific project (Project Manager only)
exports.createProjectTeam = async (req, res) => {
  try {
    const { name, description, projectId, fieldOfficers } = req.body;
    const projectManagerId = req.user.id;

    console.log('🏗️ Creating project team with data:', { name, description, projectId, fieldOfficers, projectManagerId });

    // Validate required fields
    if (!name || !projectId || !fieldOfficers || fieldOfficers.length === 0) {
      return res.status(400).json({
        status: "failed",
        message: "Please provide team name, project, and at least one field officer"
      });
    }

    // Verify project is assigned to this project manager
    const Project = require('../models/project.model');
    const project = await Project.findOne({
      _id: projectId,
      assignedTo: projectManagerId
    }).populate('location', 'name');

    if (!project) {
      return res.status(404).json({
        status: "failed",
        message: "Project not found or not assigned to you"
      });
    }

    // Check if team already exists for this project
    const existingTeam = await Team.findOne({
      project: projectId,
      projectManager: projectManagerId,
      isActive: true
    });

    if (existingTeam) {
      return res.status(400).json({
        status: "failed",
        message: "A team already exists for this project"
      });
    }

    // Verify all field officers are in the same location as the project
    const fieldOfficerRole = await Role.findOne({ name: 'fieldOfficer' });

    const validOfficers = await User.find({
      _id: { $in: fieldOfficers },
      role: fieldOfficerRole._id,
      district: project.location._id,
      isActive: { $ne: false }
    });

    if (validOfficers.length !== fieldOfficers.length) {
      return res.status(400).json({
        status: "failed",
        message: "Some selected field officers are not in the same location as the project"
      });
    }

    // Create the team
    const team = await Team.create({
      name: name.trim(),
      description: description?.trim() || "",
      project: projectId,
      projectManager: projectManagerId,
      fieldOfficers,
      createdBy: projectManagerId,
      isActive: true
    });

    console.log('✅ Project team created successfully:', team._id);

    // Populate the created team
    await team.populate([
      { path: 'project', select: 'title location', populate: { path: 'location', select: 'name' } },
      { path: 'projectManager', select: 'fullName email' },
      { path: 'fieldOfficers', select: 'fullName email phoneNumber district', populate: { path: 'district', select: 'name' } }
    ]);

    res.status(201).json({
      status: "success",
      message: "Team created successfully",
      data: team
    });

  } catch (error) {
    console.error('❌ Error creating project team:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while creating the team. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get teams created by project manager
exports.getProjectManagerTeams = async (req, res) => {
  try {
    const projectManagerId = req.user.id;
    console.log('🔍 Getting teams created by project manager:', projectManagerId);

    const teams = await Team.find({
      projectManager: projectManagerId,
      createdBy: projectManagerId, // Teams created by the PM themselves
      isActive: true
    })
    .populate({
      path: 'project',
      select: 'title location',
      populate: { path: 'location', select: 'name' }
    })
    .populate('fieldOfficers', 'fullName email phoneNumber district')
    .sort({ createdAt: -1 });

    console.log('✅ Found project manager teams:', teams.length);

    res.status(200).json({
      status: "success",
      data: teams
    });

  } catch (error) {
    console.error('❌ Error getting project manager teams:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching teams. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get field officers from team for a specific project (for activity assignment)
exports.getTeamFieldOfficersForProject = async (req, res) => {
  try {
    const { projectId } = req.params;
    const projectManagerId = req.user.id;

    console.log('🔍 Getting team field officers for project:', projectId);
    console.log('🔍 Project Manager ID:', projectManagerId);

    // Validate projectId format
    if (!mongoose.Types.ObjectId.isValid(projectId)) {
      console.log('❌ Invalid project ID format:', projectId);
      return res.status(400).json({
        status: "failed",
        message: "Invalid project ID format"
      });
    }

    // First, let's check what teams exist for this project
    const allTeamsForProject = await Team.find({ project: projectId })
      .populate('projectManager', 'fullName email')
      .populate('createdBy', 'fullName email')
      .select('name project projectManager createdBy isActive');

    console.log('🔍 All teams for project:', allTeamsForProject.map(t => ({
      id: t._id,
      name: t.name,
      projectManager: t.projectManager ? {
        id: t.projectManager._id.toString(),
        name: t.projectManager.fullName
      } : null,
      createdBy: t.createdBy ? {
        id: t.createdBy._id.toString(),
        name: t.createdBy.fullName
      } : null,
      isActive: t.isActive
    })));

    // Try to find the team with different criteria to debug the issue
    console.log('🔍 Trying different team queries...');

    // Query 1: Exact match (original)
    const teamExact = await Team.findOne({
      project: projectId,
      projectManager: projectManagerId,
      createdBy: projectManagerId,
      isActive: true
    })
    .populate('fieldOfficers', 'fullName email phoneNumber district')
    .populate('project', 'title');

    console.log('🔍 Team found with exact criteria:', !!teamExact);

    // Query 2: Just project and manager (more flexible)
    const teamFlexible = await Team.findOne({
      project: projectId,
      projectManager: projectManagerId,
      isActive: true
    })
    .populate('fieldOfficers', 'fullName email phoneNumber district')
    .populate('project', 'title');

    console.log('🔍 Team found with flexible criteria (no createdBy check):', !!teamFlexible);

    // Query 3: Just project (most flexible)
    const teamByProject = await Team.findOne({
      project: projectId,
      isActive: true
    })
    .populate('fieldOfficers', 'fullName email phoneNumber district')
    .populate('project', 'title');

    console.log('🔍 Team found by project only:', !!teamByProject);

    // Use the most specific team found
    const team = teamExact || teamFlexible || teamByProject;

    if (!team) {
      // Try alternative queries to debug
      const teamByManager = await Team.findOne({ projectManager: projectManagerId, isActive: true });

      console.log('🔍 Team by manager only:', !!teamByManager);

      return res.status(404).json({
        status: "failed",
        message: "No team found for this project. Please create a team first.",
        debug: {
          projectId,
          projectManagerId,
          allTeamsCount: allTeamsForProject.length,
          teamByProject: !!teamByProject,
          teamByManager: !!teamByManager,
          allTeamsDetails: allTeamsForProject.map(t => ({
            id: t._id,
            name: t.name,
            projectManager: t.projectManager ? {
              id: t.projectManager._id.toString(),
              name: t.projectManager.fullName
            } : null,
            createdBy: t.createdBy ? {
              id: t.createdBy._id.toString(),
              name: t.createdBy.fullName
            } : null,
            isActive: t.isActive
          }))
        }
      });
    }

    console.log('✅ Found team with field officers:', team.fieldOfficers.length);

    res.status(200).json({
      status: "success",
      data: {
        team: {
          _id: team._id,
          name: team.name,
          project: team.project
        },
        fieldOfficers: team.fieldOfficers
      }
    });

  } catch (error) {
    console.error('❌ Error getting team field officers for project:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching team members. Please try again.",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
