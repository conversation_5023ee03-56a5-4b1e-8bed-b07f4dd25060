{"mappings": ";;;;;;AAAA;;;ACAA,yBAAc,GAAG,IAAI,CAAC,KAAK,CAAC,s9EAAo8F,CAAC,CAAC;;;ADIl+F,MAAM,0BAAI,GAAG,IAAI,CAAA,GAAA,kBAAW,CAAA,CAAC,CAAA,GAAA,eAAM,CAAA,CAAC,WAAW,ooZAAgE,CAAC,AAAC;AAEjH,MAAM,0BAAI,GAAG,IAAI,CAAC,IAAI,IAAK,CAAA,CAAA,CAAC,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA,AAAC,AAAC;AACxD,MAAM,0BAAI,GAAG,CAAC,CAAC,GAAM,AAAC,0BAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAI,CAAC,AAAC,AAAC;AAExC,mDAAmD;AACnD,MAAM,mCAAa,GAAG,0BAAI,CAAC,CAAA,GAAA,gEAAI,CAAA,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,AAAC;AACvD,MAAM,oCAAc,GAAG,0BAAI,CAAC,CAAA,GAAA,gEAAI,CAAA,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,AAAC;AAC9D,MAAM,iCAAW,GAAG,0BAAI,CAAC,CAAA,GAAA,gEAAI,CAAA,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,AAAC;AAClD,MAAM,8BAAQ,GAAG,0BAAI,CAAC,CAAA,GAAA,gEAAI,CAAA,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,AAAC;AAC3C,MAAM,iCAAW,GAAG,EAAE,AAAC;AAEvB,+CAA+C;AAC/C,MAAM,oCAAc,GAAG,oCAAc,GAAG,iCAAW,GAAG,8BAAQ,GAAG,iCAAW,AAAC;AAC7E,MAAM,qCAAe,GAAG,iCAAW,GAAG,8BAAQ,GAAG,iCAAW,AAAC;AAC7D,MAAM,kCAAY,GAAG,8BAAQ,GAAG,iCAAW,AAAC;AAC5C,MAAM,+BAAS,GAAG,iCAAW,AAAC;AAC9B,MAAM,mCAAa,GAAG,AAAC,CAAA,CAAC,IAAI,mCAAa,CAAA,GAAI,CAAC,AAAC;AAC/C,MAAM,oCAAc,GAAG,AAAC,CAAA,CAAC,IAAI,oCAAc,CAAA,GAAI,CAAC,AAAC;AACjD,MAAM,iCAAW,GAAG,AAAC,CAAA,CAAC,IAAI,iCAAW,CAAA,GAAI,CAAC,AAAC;AAC3C,MAAM,8BAAQ,GAAG,AAAC,CAAA,CAAC,IAAI,8BAAQ,CAAA,GAAI,CAAC,AAAC;AACrC,MAAM,iCAAW,GAAG,AAAC,CAAA,CAAC,IAAI,iCAAW,CAAA,GAAI,CAAC,AAAC;AAEpC,SAAS,yCAAW,CAAC,SAAS,EAAE;IACrC,MAAM,GAAG,GAAG,0BAAI,CAAC,GAAG,CAAC,SAAS,CAAC,AAAC;IAChC,OAAO,CAAA,GAAA,gEAAI,CAAA,CAAC,UAAU,CAAC,AAAC,GAAG,IAAI,oCAAc,GAAI,mCAAa,CAAC,CAAC;CACjE;AAEM,SAAS,yCAAiB,CAAC,SAAS,EAAE;IAC3C,MAAM,GAAG,GAAG,0BAAI,CAAC,GAAG,CAAC,SAAS,CAAC,AAAC;IAChC,OAAO,CAAA,GAAA,gEAAI,CAAA,CAAC,gBAAgB,CAAC,AAAC,GAAG,IAAI,qCAAe,GAAI,oCAAc,CAAC,CAAC;CACzE;AAEM,SAAS,yCAAS,CAAC,SAAS,EAAE;IACnC,MAAM,GAAG,GAAG,0BAAI,CAAC,GAAG,CAAC,SAAS,CAAC,AAAC;IAChC,OAAO,CAAA,GAAA,gEAAI,CAAA,CAAC,OAAO,CAAC,AAAC,GAAG,IAAI,kCAAY,GAAI,iCAAW,CAAC,CAAC;CAC1D;AAEM,SAAS,yCAAiB,CAAC,SAAS,EAAE;IAC3C,MAAM,GAAG,GAAG,0BAAI,CAAC,GAAG,CAAC,SAAS,CAAC,AAAC;IAChC,OAAO,CAAA,GAAA,gEAAI,CAAA,CAAC,GAAG,CAAC,AAAC,GAAG,IAAI,+BAAS,GAAI,8BAAQ,CAAC,CAAC;CAChD;AAEM,SAAS,yCAAe,CAAC,SAAS,EAAE;IACzC,IAAI,GAAG,GAAG,0BAAI,CAAC,GAAG,CAAC,SAAS,CAAC,AAAC;IAC9B,IAAI,GAAG,GAAG,GAAG,GAAG,iCAAW,AAAC;IAE5B,IAAI,GAAG,KAAK,CAAC,EACX,OAAO,IAAI,CAAC;SACP,IAAI,GAAG,IAAI,EAAE,EAClB,OAAO,GAAG,GAAG,CAAC,CAAC;SACV,IAAI,GAAG,GAAG,KAAK,EAAE;QACtB,MAAM,SAAS,GAAG,AAAC,CAAA,GAAG,IAAI,CAAC,CAAA,GAAI,EAAE,AAAC;QAClC,MAAM,WAAW,GAAG,AAAC,CAAA,GAAG,GAAG,GAAG,CAAA,GAAI,CAAC,AAAC;QACpC,OAAO,SAAS,GAAG,WAAW,CAAC;KAChC,MAAM,IAAI,GAAG,GAAG,KAAK,EAAE;QACtB,GAAG,GAAG,AAAC,CAAA,GAAG,IAAI,CAAC,CAAA,GAAI,EAAE,CAAC;QACtB,IAAI,GAAG,GAAG,AAAC,CAAA,GAAG,GAAG,IAAI,CAAA,GAAI,CAAC,AAAC;QAE3B,MAAO,GAAG,GAAG,CAAC,CAAE;YACd,GAAG,IAAI,EAAE,CAAC;YACV,GAAG,EAAE,CAAC;SACP;QACD,OAAO,GAAG,CAAC;KACZ,MAAM;QACL,GAAG,GAAG,AAAC,CAAA,GAAG,IAAI,CAAC,CAAA,GAAI,IAAI,CAAC;QACxB,IAAI,GAAG,GAAG,AAAC,CAAA,GAAG,GAAG,CAAC,CAAA,GAAI,CAAC,AAAC;QACxB,MAAO,GAAG,GAAG,CAAC,CAAE;YACd,GAAG,IAAI,EAAE,CAAC;YACV,GAAG,EAAE,CAAC;SACP;QACD,OAAO,GAAG,CAAC;KACZ;CACF;AAEM,SAAS,yCAAY,CAAC,SAAS,EAAE;IACtC,MAAM,QAAQ,GAAG,yCAAW,CAAC,SAAS,CAAC,AAAC;IACxC,OACE,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,CAClB;CACF;AAEM,SAAS,yCAAO,CAAC,SAAS,EAAE;IACjC,OAAO,yCAAW,CAAC,SAAS,CAAC,KAAK,IAAI,CAAA;CACvC;AAEM,SAAS,yCAAa,CAAC,SAAS,EAAE;IACvC,MAAM,QAAQ,GAAG,yCAAW,CAAC,SAAS,CAAC,AAAC;IACxC,OACE,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,CACjB;CACH;AAEM,SAAS,yCAAW,CAAC,SAAS,EAAE;IACrC,OAAO,yCAAW,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC;CACxC;AAEM,SAAS,yCAAW,CAAC,SAAS,EAAE;IACrC,OAAO,yCAAW,CAAC,SAAS,CAAC,KAAK,IAAI,CAAA;CACvC;AAEM,SAAS,yCAAW,CAAC,SAAS,EAAE;IACrC,OAAO,yCAAW,CAAC,SAAS,CAAC,KAAK,IAAI,CAAA;CACvC;AAEM,SAAS,yCAAY,CAAC,SAAS,EAAE;IACtC,MAAM,QAAQ,GAAG,yCAAW,CAAC,SAAS,CAAC,AAAC;IACxC,OACE,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,CACjB;CACH;AAEM,SAAS,yCAAU,CAAC,SAAS,EAAE;IACpC,MAAM,QAAQ,GAAG,yCAAW,CAAC,SAAS,CAAC,AAAC;IACxC,OACE,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,CAClB;CACF;AAEM,SAAS,yCAAM,CAAC,SAAS,EAAE;IAChC,MAAM,QAAQ,GAAG,yCAAW,CAAC,SAAS,CAAC,AAAC;IACxC,OACE,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,IACjB,QAAQ,KAAK,IAAI,CACjB;CACH;IAED,2BAA2B;AAC3B,wCAeE,GAfa;iBACb,yCAAW;uBACX,yCAAiB;eACjB,yCAAS;uBACT,yCAAiB;qBACjB,yCAAe;kBACf,yCAAY;aACZ,yCAAO;mBACP,yCAAa;iBACb,yCAAW;iBACX,yCAAW;iBACX,yCAAW;kBACX,yCAAY;gBACZ,yCAAU;YACV,yCAAM;CACP", "sources": ["index.js", "data.json"], "sourcesContent": ["import base64 from 'base64-js';\nimport UnicodeTrie from 'unicode-trie';\nimport data from './data.json';\n\nconst trie = new UnicodeTrie(base64.toByteArray(require('fs').readFileSync(__dirname + '/data.trie', 'base64')));\n\nconst log2 = Math.log2 || (n => Math.log(n) / Math.LN2);\nconst bits = (n) => ((log2(n) + 1) | 0);\n\n// compute the number of bits stored for each field\nconst CATEGORY_BITS = bits(data.categories.length - 1);\nconst COMBINING_BITS = bits(data.combiningClasses.length - 1);\nconst SCRIPT_BITS = bits(data.scripts.length - 1);\nconst EAW_BITS = bits(data.eaw.length - 1);\nconst NUMBER_BITS = 10;\n\n// compute shift and mask values for each field\nconst CATEGORY_SHIFT = COMBINING_BITS + SCRIPT_BITS + EAW_BITS + NUMBER_BITS;\nconst COMBINING_SHIFT = SCRIPT_BITS + EAW_BITS + NUMBER_BITS;\nconst SCRIPT_SHIFT = EAW_BITS + NUMBER_BITS;\nconst EAW_SHIFT = NUMBER_BITS;\nconst CATEGORY_MASK = (1 << CATEGORY_BITS) - 1;\nconst COMBINING_MASK = (1 << COMBINING_BITS) - 1;\nconst SCRIPT_MASK = (1 << SCRIPT_BITS) - 1;\nconst EAW_MASK = (1 << EAW_BITS) - 1;\nconst NUMBER_MASK = (1 << NUMBER_BITS) - 1;\n\nexport function getCategory(codePoint) {\n  const val = trie.get(codePoint);\n  return data.categories[(val >> CATEGORY_SHIFT) & CATEGORY_MASK];\n}\n\nexport function getCombiningClass(codePoint) {\n  const val = trie.get(codePoint);\n  return data.combiningClasses[(val >> COMBINING_SHIFT) & COMBINING_MASK];\n}\n\nexport function getScript(codePoint) {\n  const val = trie.get(codePoint);\n  return data.scripts[(val >> SCRIPT_SHIFT) & SCRIPT_MASK];\n}\n\nexport function getEastAsianWidth(codePoint) {\n  const val = trie.get(codePoint);\n  return data.eaw[(val >> EAW_SHIFT) & EAW_MASK];\n}\n\nexport function getNumericValue(codePoint) {\n  let val = trie.get(codePoint);\n  let num = val & NUMBER_MASK;\n\n  if (num === 0) {\n    return null;\n  } else if (num <= 50) {\n    return num - 1;\n  } else if (num < 0x1e0) {\n    const numerator = (num >> 4) - 12;\n    const denominator = (num & 0xf) + 1;\n    return numerator / denominator;\n  } else if (num < 0x300) {\n    val = (num >> 5) - 14;\n    let exp = (num & 0x1f) + 2;\n\n    while (exp > 0) {\n      val *= 10;\n      exp--;\n    }\n    return val;\n  } else {\n    val = (num >> 2) - 0xbf;\n    let exp = (num & 3) + 1;\n    while (exp > 0) {\n      val *= 60;\n      exp--;\n    }\n    return val;\n  }\n}\n\nexport function isAlphabetic(codePoint) {\n  const category = getCategory(codePoint);\n  return (\n    category === 'Lu' ||\n    category === 'Ll' ||\n    category === 'Lt' ||\n    category === 'Lm' ||\n    category === 'Lo' ||\n    category === 'Nl'\n  )\n}\n\nexport function isDigit(codePoint) {\n  return getCategory(codePoint) === 'Nd'\n}\n\nexport function isPunctuation(codePoint) {\n  const category = getCategory(codePoint);\n  return (\n    category === 'Pc' ||\n    category === 'Pd' ||\n    category === 'Pe' ||\n    category === 'Pf' ||\n    category === 'Pi' ||\n    category === 'Po' ||\n    category === 'Ps'\n  );\n}\n\nexport function isLowerCase(codePoint) {\n  return getCategory(codePoint) === 'Ll';\n}\n\nexport function isUpperCase(codePoint) {\n  return getCategory(codePoint) === 'Lu'\n}\n\nexport function isTitleCase(codePoint) {\n  return getCategory(codePoint) === 'Lt'\n}\n\nexport function isWhiteSpace(codePoint) {\n  const category = getCategory(codePoint);\n  return (\n    category === 'Zs' ||\n    category === 'Zl' ||\n    category === 'Zp'\n  );\n}\n\nexport function isBaseForm(codePoint) {\n  const category = getCategory(codePoint);\n  return (\n    category === 'Nd' ||\n    category === 'No' ||\n    category === 'Nl' ||\n    category === 'Lu' ||\n    category === 'Ll' ||\n    category === 'Lt' ||\n    category === 'Lm' ||\n    category === 'Lo' ||\n    category === 'Me' ||\n    category === 'Mc'\n  )\n}\n\nexport function isMark(codePoint) {\n  const category = getCategory(codePoint);\n  return (\n    category === 'Mn' ||\n    category === 'Me' ||\n    category === 'Mc'\n  );\n}\n\n// Backwards compatibility.\nexport default {\n  getCategory,\n  getCombiningClass,\n  getScript,\n  getEastAsianWidth,\n  getNumericValue,\n  isAlphabetic,\n  isDigit,\n  isPunctuation,\n  isLowerCase,\n  isUpperCase,\n  isTitleCase,\n  isWhiteSpace,\n  isBaseForm,\n  isMark\n};\n", "{\"categories\":[\"Cc\",\"Zs\",\"Po\",\"Sc\",\"Ps\",\"Pe\",\"Sm\",\"Pd\",\"Nd\",\"Lu\",\"Sk\",\"Pc\",\"Ll\",\"So\",\"Lo\",\"Pi\",\"Cf\",\"No\",\"Pf\",\"Lt\",\"Lm\",\"Mn\",\"Me\",\"Mc\",\"Nl\",\"Zl\",\"Zp\",\"Cs\",\"Co\"],\"combiningClasses\":[\"Not_Reordered\",\"Above\",\"Above_Right\",\"Below\",\"Attached_Above_Right\",\"Attached_Below\",\"Overlay\",\"Iota_Subscript\",\"Double_Below\",\"Double_Above\",\"Below_Right\",\"Above_Left\",\"CCC10\",\"CCC11\",\"CCC12\",\"CCC13\",\"CCC14\",\"CCC15\",\"CCC16\",\"CCC17\",\"CCC18\",\"CCC19\",\"CCC20\",\"CCC21\",\"CCC22\",\"CCC23\",\"CCC24\",\"CCC25\",\"CCC30\",\"CCC31\",\"CCC32\",\"CCC27\",\"CCC28\",\"CCC29\",\"CCC33\",\"CCC34\",\"CCC35\",\"CCC36\",\"Nukta\",\"Virama\",\"CCC84\",\"CCC91\",\"CCC103\",\"CCC107\",\"CCC118\",\"CCC122\",\"CCC129\",\"CCC130\",\"CCC132\",\"Attached_Above\",\"Below_Left\",\"Left\",\"Kana_Voicing\",\"CCC26\",\"Right\"],\"scripts\":[\"Common\",\"Latin\",\"Bopomofo\",\"Inherited\",\"Greek\",\"Coptic\",\"Cyrillic\",\"Armenian\",\"Hebrew\",\"Arabic\",\"Syriac\",\"Thaana\",\"Nko\",\"Samaritan\",\"Mandaic\",\"Devanagari\",\"Bengali\",\"Gurmukhi\",\"Gujarati\",\"Oriya\",\"Tamil\",\"Telugu\",\"Kannada\",\"Malayalam\",\"Sinhala\",\"Thai\",\"Lao\",\"Tibetan\",\"Myanmar\",\"Georgian\",\"Hangul\",\"Ethiopic\",\"Cherokee\",\"Canadian_Aboriginal\",\"Ogham\",\"Runic\",\"Tagalog\",\"Hanunoo\",\"Buhid\",\"Tagbanwa\",\"Khmer\",\"Mongolian\",\"Limbu\",\"Tai_Le\",\"New_Tai_Lue\",\"Buginese\",\"Tai_Tham\",\"Balinese\",\"Sundanese\",\"Batak\",\"Lepcha\",\"Ol_Chiki\",\"Braille\",\"Glagolitic\",\"Tifinagh\",\"Han\",\"Hiragana\",\"Katakana\",\"Yi\",\"Lisu\",\"Vai\",\"Bamum\",\"Syloti_Nagri\",\"Phags_Pa\",\"Saurashtra\",\"Kayah_Li\",\"Rejang\",\"Javanese\",\"Cham\",\"Tai_Viet\",\"Meetei_Mayek\",\"null\",\"Linear_B\",\"Lycian\",\"Carian\",\"Old_Italic\",\"Gothic\",\"Old_Permic\",\"Ugaritic\",\"Old_Persian\",\"Deseret\",\"Shavian\",\"Osmanya\",\"Osage\",\"Elbasan\",\"Caucasian_Albanian\",\"Linear_A\",\"Cypriot\",\"Imperial_Aramaic\",\"Palmyrene\",\"Nabataean\",\"Hatran\",\"Phoenician\",\"Lydian\",\"Meroitic_Hieroglyphs\",\"Meroitic_Cursive\",\"Kharoshthi\",\"Old_South_Arabian\",\"Old_North_Arabian\",\"Manichaean\",\"Avestan\",\"Inscriptional_Parthian\",\"Inscriptional_Pahlavi\",\"Psalter_Pahlavi\",\"Old_Turkic\",\"Old_Hungarian\",\"Hanifi_Rohingya\",\"Old_Sogdian\",\"Sogdian\",\"Elymaic\",\"Brahmi\",\"Kaithi\",\"Sora_Sompeng\",\"Chakma\",\"Mahajani\",\"Sharada\",\"Khojki\",\"Multani\",\"Khudawadi\",\"Grantha\",\"Newa\",\"Tirhuta\",\"Siddham\",\"Modi\",\"Takri\",\"Ahom\",\"Dogra\",\"Warang_Citi\",\"Nandinagari\",\"Zanabazar_Square\",\"Soyombo\",\"Pau_Cin_Hau\",\"Bhaiksuki\",\"Marchen\",\"Masaram_Gondi\",\"Gunjala_Gondi\",\"Makasar\",\"Cuneiform\",\"Egyptian_Hieroglyphs\",\"Anatolian_Hieroglyphs\",\"Mro\",\"Bassa_Vah\",\"Pahawh_Hmong\",\"Medefaidrin\",\"Miao\",\"Tangut\",\"Nushu\",\"Duployan\",\"SignWriting\",\"Nyiakeng_Puachue_Hmong\",\"Wancho\",\"Mende_Kikakui\",\"Adlam\"],\"eaw\":[\"N\",\"Na\",\"A\",\"W\",\"H\",\"F\"]}"], "names": [], "version": 3, "file": "module.mjs.map"}