const ActivityReport = require("../models/activityReport.model");
const ProjectActivies = require("../models/projectActivities.model");
const Project = require("../models/project.model");
const User = require("../models/user.model");
const NotificationService = require("../services/notificationService");
const { sendReportSubmissionEmail } = require("../services/reportNotificationService");

exports.createActivityReport = async (req, res) => {
  try {
    console.log('🔍 Debug - Activity Report Request:');
    console.log('   User:', req.user?.fullName);
    console.log('   User ID:', req.user?.id);
    console.log('   User Role:', req.user?.role?.name);
    console.log('   Request Body:', req.body);

    const { activity_id, description, customFields, attachments, amountSpent } = req.body;
    const submittedBy = req.user.id; // Get from authenticated user

    console.log('💰 Amount spent received:', amountSpent, 'Type:', typeof amountSpent);

    // Handle both old format (activity, content) and new format (activity_id, description)
    const activityId = activity_id || req.body.activity;
    const reportContent = description || req.body.content;

    console.log('🔄 Creating activity report:', {
      activityId,
      submittedBy,
      contentLength: reportContent?.length,
      customFields: customFields ? Object.keys(customFields).length : 0
    });

    // Validate required fields
    if (!activityId) {
      console.log('❌ Missing activity_id');
      return res.status(400).json({
        status: "failed",
        message: "Activity ID is required",
      });
    }

    if (!reportContent) {
      console.log('❌ Missing description');
      return res.status(400).json({
        status: "failed",
        message: "Report description is required",
      });
    }

    const existingReport = await ActivityReport.findOne({ activity: activityId });
    if (existingReport) {
      console.log('❌ Report already exists for activity:', activityId);
      return res.status(400).json({
        status: "failed",
        message: "Report for this activity already submitted",
      });
    }

    // Get activity with populated project and project manager
    const activityData = await ProjectActivies.findById(activityId)
      .populate({
        path: 'project',
        populate: {
          path: 'assignedTo',
          select: 'fullName email'
        }
      });

    if (!activityData) {
      console.log('❌ Activity not found:', activityId);
      return res.status(404).json({
        status: "failed",
        message: "Activity not found",
      });
    }

    console.log('🔍 Activity assignment check:', {
      activityAssignedTo: activityData.assignedTo?.toString(),
      submittedBy: submittedBy,
      match: activityData.assignedTo?.toString() === submittedBy
    });

    // Verify the user is assigned to this activity
    if (activityData.assignedTo?.toString() !== submittedBy) {
      console.log('❌ User not assigned to activity');
      return res.status(403).json({
        status: "failed",
        message: "You can only submit reports for activities assigned to you",
      });
    }

    // Get the project manager (who will receive the report)
    const projectManager = activityData.project?.assignedTo;
    console.log('🔍 Project manager check:', {
      hasProject: !!activityData.project,
      hasProjectManager: !!projectManager,
      projectManagerId: projectManager?._id || projectManager
    });

    if (!projectManager) {
      console.log('❌ No project manager assigned');
      return res.status(400).json({
        status: "failed",
        message: "No project manager assigned to this project",
      });
    }

    console.log('📋 Report will be submitted to:', {
      projectManager: projectManager.fullName,
      email: projectManager.email
    });

    // Prepare content array for the report
    let contentArray = [];

    // Add main description
    contentArray.push({
      fieldName: 'description',
      entry: reportContent,
      required: true
    });

    // Add custom fields if provided
    if (customFields && typeof customFields === 'object') {
      Object.entries(customFields).forEach(([fieldName, value]) => {
        contentArray.push({
          fieldName,
          entry: String(value),
          required: false
        });
      });
    }

    let createdReport;
    const parsedAmountSpent = parseFloat(amountSpent) || 0;
    console.log('💰 Parsed amount spent:', parsedAmountSpent);

    // Check if submission is late
    const now = new Date();
    const dueDate = new Date(activityData.dueDate);
    const isLate = now > dueDate;
    const lateDays = isLate ? Math.ceil((now - dueDate) / (1000 * 60 * 60 * 24)) : 0;

    const reportData = {
      activity: activityId,
      submittedBy,
      submitTo: projectManager._id, // Set the project manager as recipient
      content: contentArray,
      amountSpent: parsedAmountSpent,
      status: isLate ? "lateSubmission" : "onTime",
      lateDays: isLate ? lateDays : undefined
    };

    console.log('📋 Report data to be saved:', reportData);

    if (activityData.endDate && new Date() > new Date(activityData.endDate)) {
      const diffInMs = new Date() - new Date(activityData.endDate);
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

      reportData.lateDays = diffInDays;
      reportData.status = "lateSubmission";
    }

    createdReport = await ActivityReport.create(reportData);

    console.log('✅ Report created with amountSpent:', createdReport.amountSpent);

    // Don't mark activity as completed yet - wait for project manager approval
    // await ProjectActivies.findByIdAndUpdate(activityId, { status: "completed" });

    // Get field officer details for notifications
    const fieldOfficer = await User.findById(submittedBy).select('fullName email');

    // Send in-app notification to project manager
    try {
      await NotificationService.notifyActivityReportSubmitted(createdReport._id);
      console.log('✅ In-app notification sent to project manager');

      // If submission is late, send additional late submission notification
      if (isLate) {
        await NotificationService.notifyLateReportSubmission(activityId);
        console.log('⚠️ Late submission notification sent to project manager');
      }
    } catch (notificationError) {
      console.error('❌ Failed to send in-app notification:', notificationError);
    }

    // Send email notification to project manager
    try {
      const emailData = {
        projectManager: projectManager,
        fieldOfficer: fieldOfficer,
        activity: {
          _id: activityData._id,
          title: activityData.title
        },
        project: {
          _id: activityData.project._id,
          name: activityData.project.name
        },
        reportId: createdReport._id,
        submissionStatus: reportData.status
      };

      await sendReportSubmissionEmail(emailData);
      console.log('✅ Email notification sent to project manager');
    } catch (emailError) {
      console.error('❌ Failed to send email notification:', emailError);
    }

    res.status(201).json({
      status: "success",
      message: "Activity report submitted successfully. Project manager has been notified.",
      report: {
        _id: createdReport._id,
        activity: createdReport.activity,
        submittedBy: createdReport.submittedBy,
        submitTo: createdReport.submitTo,
        status: createdReport.status,
        submittedAt: createdReport.createdAt
      },
    });
  } catch (error) {
    console.error("❌ Activity report creation error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

exports.getAllReports = async (req, res) => {
  try {
    const reports = await ActivityReport.find({});
    res.status(200).json({ status: "success", reports });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.approveReport = async (req, res) => {
  try {
    const { id } = req.params;
    const approvedReport = await ActivityReport.findByIdAndUpdate(
      id,
      { approved: true },
      { $new: true }
    );

    if (!approvedReport) {
      return res
        .status(404)
        .json({ status: "failed", message: "Report not found" });
    }

    res
      .status(200)
      .json({ status: "sucess", message: "Report approved successfully!!" });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};
