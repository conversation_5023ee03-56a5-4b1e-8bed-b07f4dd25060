const express = require('express');
const router = express.Router();
const { protect } = require("../middleware/auth");

const {
  getProjectTrackingStatus,
  getMultipleProjectsTracking,
  getProjectTrackingSummary,
  getProjectsByStatus,
  getProjectAlerts,
  getProjectRecommendations,
  refreshProjectTracking,
  getDashboardTrackingMetrics
} = require("../controllers/projectTracking.controller");

// Project tracking routes
router.get("/summary", protect, getProjectTrackingSummary);
router.get("/dashboard-metrics", protect, getDashboardTrackingMetrics);
router.get("/alerts", protect, getProjectAlerts);
router.get("/status/:trackingStatus", protect, getProjectsByStatus);
router.get("/:projectId", protect, getProjectTrackingStatus);
router.get("/:projectId/recommendations", protect, getProjectRecommendations);
router.post("/multiple", protect, getMultipleProjectsTracking);
router.post("/:projectId/refresh", protect, refreshProjectTracking);

module.exports = router;
