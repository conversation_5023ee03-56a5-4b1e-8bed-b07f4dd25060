const express = require('express');
const router = express.Router();
const { protect } = require("../middleware/auth");

const {
  getReportsData,
  getBudgetAnalytics,
  getProjectAnalytics,
  getActivityAnalytics,
  exportProjectAnalyticsPDF,
} = require("../controllers/reports.controller");

// Reports routes
router.get("/data", protect, getReportsData);
router.get("/budget-analytics", protect, getBudgetAnalytics);
router.get("/project-analytics", protect, getProjectAnalytics);
router.get("/activity-analytics", protect, getActivityAnalytics);
router.get("/export/project-analytics", protect, exportProjectAnalyticsPDF);

module.exports = router;
