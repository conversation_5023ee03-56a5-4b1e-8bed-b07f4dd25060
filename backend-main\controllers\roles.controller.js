const Role = require("../models/roles.model");

exports.addRole = async (req, res) => {
  try {
    const { name } = req.body;

    const existingRole = await Role.findOne({ name });
    if (existingRole) {
      return res
        .status(400)
        .json({ status: "failed", message: "Role already exist" });
    }

    await Role.create({ name });

    res
      .status(201)
      .json({ status: "success", message: "Role added successfully!!" });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.getAllRoles = async (req, res) => {
  try {
    const roles = await Role.find({});
    res.status(200).json({ status: "sucess", roles });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.deleteRole = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedRole = await Role.findByIdAndDelete(id);

    if (!deletedRole) {
      return res
        .status(404)
        .json({ status: "failed", message: "Role not found!!" });
    }

    res
      .status(200)
      .json({ status: "success", message: "Role deleted successfully!!" });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.updateRole = async (req, res) => {
  try {
    const { id } = req.params;

    const { name } = req.body;

    const existingRole = await Role.findOne({ name });
    if (existingRole) {
      return res
        .status(400)
        .json({ status: "failed", message: "Role already exist" });
    }

    const updatedRole = await Role.findByIdAndUpdate(
      id,
      { name },
      { $new: true }
    );

    if (!updatedRole) {
      return res
        .status(404)
        .json({ status: "failed", message: "Role not found!!" });
    }

    res
      .status(200)
      .json({ status: "success", message: "Role updated successfully!!" });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};
