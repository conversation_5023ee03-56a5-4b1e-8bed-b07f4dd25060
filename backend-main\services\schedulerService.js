const cron = require("node-cron");
const NotificationService = require("./notificationService");
const BudgetMonitoringService = require("./budgetMonitoringService");

class SchedulerService {
  static init() {
    console.log('🕐 Initializing enhanced scheduler service...');

    // Run activity due reminders every day at 9 AM
    cron.schedule("0 9 * * *", async () => {
      console.log('⏰ Running daily activity due reminders...');
      try {
        await NotificationService.sendActivityDueReminders();
      } catch (error) {
        console.error('Error in daily activity due reminders:', error);
      }
    });

    // Check for late activities every day at 10 AM
    cron.schedule("0 10 * * *", async () => {
      console.log('🚨 Running daily late activity flagging...');
      try {
        await NotificationService.flagLateActivities();
      } catch (error) {
        console.error('Error in daily late activity flagging:', error);
      }
    });

    // Run budget monitoring every 6 hours
    cron.schedule("0 */6 * * *", async () => {
      console.log('💰 Running budget monitoring checks...');
      try {
        await BudgetMonitoringService.runAllChecks();
      } catch (error) {
        console.error('Error in budget monitoring:', error);
      }
    });

    // Run comprehensive monitoring every day at 8 AM
    cron.schedule("0 8 * * *", async () => {
      console.log('📊 Running comprehensive daily monitoring...');
      try {
        await BudgetMonitoringService.checkBudgetUtilization();
        await BudgetMonitoringService.checkScheduleSlippage();
        await BudgetMonitoringService.monitorProjectProgress();
        await NotificationService.sendActivityDueReminders();
      } catch (error) {
        console.error('Error in comprehensive monitoring:', error);
      }
    });

    // Run urgent checks every hour during business hours (8 AM - 6 PM)
    cron.schedule("0 8-18 * * *", async () => {
      console.log('🔍 Running hourly urgent checks...');
      try {
        await BudgetMonitoringService.checkBudgetUtilization();
      } catch (error) {
        console.error('Error in hourly checks:', error);
      }
    });

    console.log('✅ Enhanced scheduler service initialized');
  }
}

module.exports = SchedulerService;
