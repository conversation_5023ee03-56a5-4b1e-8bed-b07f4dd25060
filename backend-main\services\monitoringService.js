const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");
const Project = require("../models/project.model");
const User = require("../models/user.model");
const NotificationService = require("./notificationService");
const BudgetMonitoringService = require("./budgetMonitoringService");

class MonitoringService {
  // Check for upcoming activity deadlines and send reminders
  static async checkUpcomingDeadlines() {
    try {
      console.log('📅 Checking for upcoming activity deadlines...');
      
      const now = new Date();
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      const threeDaysFromNow = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);

      // Find activities due within 3 days that haven't been completed
      const upcomingActivities = await ProjectActivities.find({
        dueDate: { $gte: now, $lte: threeDaysFromNow },
        status: { $in: ['pending', 'in-progress'] }
      }).populate('assignedTo project');

      for (const activity of upcomingActivities) {
        const daysUntilDue = Math.ceil((new Date(activity.dueDate) - now) / (1000 * 60 * 60 * 24));
        
        if (daysUntilDue <= 1) {
          // Send urgent reminder for activities due tomorrow or today
          await NotificationService.createNotification({
            recipient: activity.assignedTo._id,
            type: "activity_due_reminder",
            title: "Urgent: Activity Due Soon",
            message: `Activity "${activity.title}" is due ${daysUntilDue === 0 ? 'today' : 'tomorrow'}`,
            priority: "urgent",
            relatedEntity: {
              entityType: "activity",
              entityId: activity._id,
            },
            actionUrl: `/field/activities/${activity._id}`,
          });
        } else {
          // Send regular reminder for activities due within 3 days
          await NotificationService.createNotification({
            recipient: activity.assignedTo._id,
            type: "activity_due_reminder",
            title: "Activity Deadline Reminder",
            message: `Activity "${activity.title}" is due in ${daysUntilDue} days`,
            priority: "medium",
            relatedEntity: {
              entityType: "activity",
              entityId: activity._id,
            },
            actionUrl: `/field/activities/${activity._id}`,
          });
        }
      }

      console.log(`✅ Processed ${upcomingActivities.length} upcoming activities`);
    } catch (error) {
      console.error("Error checking upcoming deadlines:", error);
    }
  }

  // Check for overdue activities and send alerts
  static async checkOverdueActivities() {
    try {
      console.log('⚠️ Checking for overdue activities...');
      
      const now = new Date();

      // Find activities that are overdue and not completed
      const overdueActivities = await ProjectActivities.find({
        dueDate: { $lt: now },
        status: { $in: ['pending', 'in-progress'] }
      }).populate('assignedTo project');

      for (const activity of overdueActivities) {
        const daysOverdue = Math.ceil((now - new Date(activity.dueDate)) / (1000 * 60 * 60 * 24));
        
        // Notify field officer about overdue activity
        await NotificationService.createNotification({
          recipient: activity.assignedTo._id,
          type: "activity_overdue",
          title: "Overdue Activity",
          message: `Activity "${activity.title}" is ${daysOverdue} day${daysOverdue > 1 ? 's' : ''} overdue`,
          priority: "urgent",
          relatedEntity: {
            entityType: "activity",
            entityId: activity._id,
          },
          actionUrl: `/field/activities/${activity._id}`,
        });

        // Notify project manager about overdue activity
        const project = await Project.findById(activity.project._id).populate('assignedTo');
        if (project && project.assignedTo) {
          await NotificationService.createNotification({
            recipient: project.assignedTo._id,
            type: "activity_overdue",
            title: "Team Member Has Overdue Activity",
            message: `${activity.assignedTo.fullName} has an overdue activity: "${activity.title}" (${daysOverdue} days overdue)`,
            priority: "high",
            relatedEntity: {
              entityType: "activity",
              entityId: activity._id,
            },
            actionUrl: `/manager/activities`,
          });
        }
      }

      console.log(`⚠️ Processed ${overdueActivities.length} overdue activities`);
    } catch (error) {
      console.error("Error checking overdue activities:", error);
    }
  }

  // Check for late report submissions
  static async checkLateReportSubmissions() {
    try {
      console.log('📄 Checking for late report submissions...');
      
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // Find activities that were completed but reports submitted late
      const lateReports = await ActivityReport.find({
        status: "lateSubmission",
        createdAt: { $gte: oneDayAgo } // Only check recent late submissions
      }).populate('activity submittedBy');

      for (const report of lateReports) {
        // Get project manager for this activity
        const project = await Project.findById(report.activity.project).populate('assignedTo');
        
        if (project && project.assignedTo) {
          await NotificationService.notifyLateReportSubmission(report.activity._id);
        }
      }

      console.log(`📄 Processed ${lateReports.length} late report submissions`);
    } catch (error) {
      console.error("Error checking late report submissions:", error);
    }
  }

  // Monitor project progress and detect issues
  static async monitorProjectHealth() {
    try {
      console.log('🏥 Monitoring project health...');
      
      const projects = await Project.find({ status: 'inprogress' });

      for (const project of projects) {
        // Check budget utilization
        await BudgetMonitoringService.checkProjectBudget(project._id);

        // Check schedule adherence
        const activities = await ProjectActivities.find({ project: project._id });
        const overdueActivities = activities.filter(activity => 
          new Date(activity.dueDate) < new Date() && 
          activity.status !== 'completed'
        );

        if (overdueActivities.length > 0) {
          const totalActivities = activities.length;
          const overduePercentage = (overdueActivities.length / totalActivities) * 100;

          // If more than 30% of activities are overdue, flag schedule slippage
          if (overduePercentage > 30) {
            const averageDelay = overdueActivities.reduce((sum, activity) => {
              const delay = Math.ceil((new Date() - new Date(activity.dueDate)) / (1000 * 60 * 60 * 24));
              return sum + delay;
            }, 0) / overdueActivities.length;

            await NotificationService.notifyScheduleSlippage(project._id, Math.ceil(averageDelay));
          }
        }
      }

      console.log(`🏥 Monitored ${projects.length} projects`);
    } catch (error) {
      console.error("Error monitoring project health:", error);
    }
  }

  // Run all monitoring checks
  static async runAllChecks() {
    console.log('🔄 Starting comprehensive monitoring checks...');
    
    try {
      await this.checkUpcomingDeadlines();
      await this.checkOverdueActivities();
      await this.checkLateReportSubmissions();
      await this.monitorProjectHealth();
      await BudgetMonitoringService.runAllChecks();
      
      console.log('✅ All monitoring checks completed successfully');
    } catch (error) {
      console.error('❌ Error during monitoring checks:', error);
    }
  }

  // Run urgent checks (more frequent)
  static async runUrgentChecks() {
    console.log('🚨 Running urgent monitoring checks...');
    
    try {
      await this.checkUpcomingDeadlines();
      await this.checkOverdueActivities();
      await BudgetMonitoringService.checkBudgetUtilization();
      
      console.log('✅ Urgent monitoring checks completed');
    } catch (error) {
      console.error('❌ Error during urgent monitoring checks:', error);
    }
  }

  // Initialize monitoring with different schedules
  static initializeMonitoring() {
    console.log('🎯 Initializing comprehensive monitoring system...');
    
    // Run comprehensive checks every 6 hours
    setInterval(() => {
      this.runAllChecks();
    }, 6 * 60 * 60 * 1000);

    // Run urgent checks every hour during business hours
    setInterval(() => {
      const now = new Date();
      const hour = now.getHours();
      
      // Only run during business hours (8 AM - 6 PM)
      if (hour >= 8 && hour <= 18) {
        this.runUrgentChecks();
      }
    }, 60 * 60 * 1000);

    // Run initial check after 30 seconds
    setTimeout(() => {
      this.runAllChecks();
    }, 30000);

    console.log('✅ Monitoring system initialized');
  }
}

module.exports = MonitoringService;
