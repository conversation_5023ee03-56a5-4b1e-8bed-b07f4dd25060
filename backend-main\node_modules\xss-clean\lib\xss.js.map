{"version": 3, "sources": ["../src/xss.js"], "names": ["clean", "data", "isObject", "JSON", "stringify", "trim", "parse"], "mappings": ";;;;;;;;QAOgBA,K,GAAAA,K;;AAPhB;;AAOO,SAASA,KAAT,GAA2B;AAAA,MAAXC,IAAW,uEAAJ,EAAI;;AAChC,MAAIC,WAAW,KAAf;AACA,MAAI,QAAOD,IAAP,yCAAOA,IAAP,OAAgB,QAApB,EAA8B;AAC5BA,WAAOE,KAAKC,SAAL,CAAeH,IAAf,CAAP;AACAC,eAAW,IAAX;AACD;;AAEDD,SAAO,4BAAWA,IAAX,EAAiBI,IAAjB,EAAP;AACA,MAAIH,QAAJ,EAAcD,OAAOE,KAAKG,KAAL,CAAWL,IAAX,CAAP;;AAEd,SAAOA,IAAP;AACD", "file": "xss.js", "sourcesContent": ["import { inHTMLData } from 'xss-filters'\n\n/**\n * Clean for xss.\n * @param {string/object} data - The value to sanitize\n * @return {string/object} The sanitized value\n */\nexport function clean (data = '') {\n  let isObject = false\n  if (typeof data === 'object') {\n    data = JSON.stringify(data)\n    isObject = true\n  }\n\n  data = inHTMLData(data).trim()\n  if (isObject) data = JSON.parse(data)\n\n  return data\n}\n"]}