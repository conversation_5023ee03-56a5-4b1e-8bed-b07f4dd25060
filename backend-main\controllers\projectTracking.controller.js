const ProjectTrackingService = require("../services/projectTrackingService");
const Project = require("../models/project.model");

// Get tracking status for a specific project
exports.getProjectTrackingStatus = async (req, res) => {
  try {
    const { projectId } = req.params;
    
    const trackingStatus = await ProjectTrackingService.evaluateProjectStatus(projectId);
    
    res.status(200).json({
      status: "success",
      data: trackingStatus
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: error.message || "Failed to get project tracking status"
    });
  }
};

// Get tracking status for multiple projects
exports.getMultipleProjectsTracking = async (req, res) => {
  try {
    const { projectIds } = req.body;
    
    if (!Array.isArray(projectIds)) {
      return res.status(400).json({
        status: "error",
        message: "Project IDs must be provided as an array"
      });
    }
    
    const trackingResults = await ProjectTrackingService.evaluateMultipleProjects(projectIds);
    
    res.status(200).json({
      status: "success",
      data: trackingResults
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to get projects tracking status"
    });
  }
};

// Get overall project tracking summary
exports.getProjectTrackingSummary = async (req, res) => {
  try {
    const summary = await ProjectTrackingService.getProjectTrackingSummary();
    
    res.status(200).json({
      status: "success",
      data: summary
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to get project tracking summary"
    });
  }
};

// Get projects by tracking status
exports.getProjectsByStatus = async (req, res) => {
  try {
    const { trackingStatus } = req.params;
    
    const validStatuses = ['on_track', 'warning', 'at_risk', 'critical'];
    if (!validStatuses.includes(trackingStatus)) {
      return res.status(400).json({
        status: "error",
        message: "Invalid tracking status"
      });
    }
    
    const summary = await ProjectTrackingService.getProjectTrackingSummary();
    const filteredProjects = summary.projects.filter(p => p.overallStatus === trackingStatus);
    
    res.status(200).json({
      status: "success",
      data: {
        trackingStatus,
        count: filteredProjects.length,
        projects: filteredProjects
      }
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to get projects by status"
    });
  }
};

// Get project alerts
exports.getProjectAlerts = async (req, res) => {
  try {
    const summary = await ProjectTrackingService.getProjectTrackingSummary();
    
    // Collect all alerts from all projects
    const allAlerts = [];
    summary.projects.forEach(project => {
      if (project.alerts && project.alerts.length > 0) {
        project.alerts.forEach(alert => {
          allAlerts.push({
            ...alert,
            projectId: project.projectId,
            projectName: project.projectName
          });
        });
      }
    });
    
    // Sort alerts by priority (critical first)
    allAlerts.sort((a, b) => {
      const priorityOrder = { 'critical': 0, 'warning': 1, 'info': 2 };
      return priorityOrder[a.type] - priorityOrder[b.type];
    });
    
    res.status(200).json({
      status: "success",
      data: {
        totalAlerts: allAlerts.length,
        criticalAlerts: allAlerts.filter(a => a.type === 'critical').length,
        warningAlerts: allAlerts.filter(a => a.type === 'warning').length,
        alerts: allAlerts
      }
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to get project alerts"
    });
  }
};

// Get project recommendations
exports.getProjectRecommendations = async (req, res) => {
  try {
    const { projectId } = req.params;
    
    const trackingStatus = await ProjectTrackingService.evaluateProjectStatus(projectId);
    
    res.status(200).json({
      status: "success",
      data: {
        projectId,
        projectName: trackingStatus.projectName,
        overallStatus: trackingStatus.overallStatus,
        recommendations: trackingStatus.recommendations
      }
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to get project recommendations"
    });
  }
};

// Update project tracking (manual refresh)
exports.refreshProjectTracking = async (req, res) => {
  try {
    const { projectId } = req.params;
    
    // Verify project exists
    const project = await Project.findById(projectId);
    if (!project) {
      return res.status(404).json({
        status: "error",
        message: "Project not found"
      });
    }
    
    // Get fresh tracking status
    const trackingStatus = await ProjectTrackingService.evaluateProjectStatus(projectId);
    
    res.status(200).json({
      status: "success",
      message: "Project tracking refreshed successfully",
      data: trackingStatus
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to refresh project tracking"
    });
  }
};

// Get dashboard tracking metrics
exports.getDashboardTrackingMetrics = async (req, res) => {
  try {
    const summary = await ProjectTrackingService.getProjectTrackingSummary();
    
    // Calculate additional metrics for dashboard
    const totalProjects = summary.summary.totalProjects;
    const healthyProjects = summary.summary.onTrack + summary.summary.warning;
    const problematicProjects = summary.summary.atRisk + summary.summary.critical;
    
    const healthPercentage = totalProjects > 0 ? Math.round((healthyProjects / totalProjects) * 100) : 0;
    
    // Get recent alerts
    const allAlerts = [];
    summary.projects.forEach(project => {
      if (project.alerts && project.alerts.length > 0) {
        project.alerts.forEach(alert => {
          allAlerts.push({
            ...alert,
            projectId: project.projectId,
            projectName: project.projectName,
            timestamp: new Date()
          });
        });
      }
    });
    
    const recentAlerts = allAlerts
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);
    
    res.status(200).json({
      status: "success",
      data: {
        summary: summary.summary,
        metrics: {
          totalProjects,
          healthyProjects,
          problematicProjects,
          healthPercentage,
          criticalIssues: summary.summary.critical,
          atRiskProjects: summary.summary.atRisk
        },
        recentAlerts,
        chartData: {
          statusDistribution: {
            labels: ['On Track', 'Warning', 'At Risk', 'Critical'],
            data: [
              summary.summary.onTrack,
              summary.summary.warning,
              summary.summary.atRisk,
              summary.summary.critical
            ],
            backgroundColor: ['#10B981', '#F59E0B', '#F97316', '#EF4444']
          }
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to get dashboard tracking metrics"
    });
  }
};
