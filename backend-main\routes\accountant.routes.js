const express = require('express');
const router = express.Router();
const { protect } = require("../middleware/auth");

const {
  getAccountantDashboardStats,
  getBudgetMonitoringData,
  getProjectsDebug,
  getFinancialReports,
} = require("../controllers/accountant.controller");

// Dashboard routes
router.get("/dashboard/stats", protect, getAccountantDashboardStats);

// Budget monitoring routes
router.get("/budgets/monitoring", protect, getBudgetMonitoringData);

// Debug routes
router.get("/debug/projects", protect, getProjectsDebug);

// Financial reports
router.get("/reports/financial", protect, getFinancialReports);

module.exports = router;
