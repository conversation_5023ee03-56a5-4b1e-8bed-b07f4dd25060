const express = require("express");
const {
  getNotifications,
  mark<PERSON><PERSON><PERSON>,
  markAllAs<PERSON><PERSON>,
  getUnreadCount
} = require("../controllers/notifications.controller");
const { protect } = require("../middleware/auth");

const router = express.Router();

// Health check endpoint
router.get("/health", (req, res) => {
  res.status(200).json({
    status: "success",
    message: "Notifications service is running",
    timestamp: new Date().toISOString()
  });
});

router.get("/", protect, getNotifications);
router.get("/unread-count", protect, getUnreadCount);
router.patch("/:notificationId/read", protect, markAsRead);
router.patch("/mark-all-read", protect, markAllAsRead);

// Test endpoint to create a test notification
router.post("/test", protect, async (req, res) => {
  try {
    const NotificationService = require('../services/notificationService');
    const notification = await NotificationService.createNotification({
      recipient: req.user._id || req.user.id,
      type: 'test_notification',
      title: 'Test Notification',
      message: 'This is a test notification to verify the system is working.',
      priority: 'medium'
    });

    res.status(200).json({
      status: "success",
      message: "Test notification created successfully",
      data: notification
    });
  } catch (error) {
    console.error('❌ Test notification error:', error);
    res.status(500).json({
      status: "error",
      message: "Failed to create test notification",
      error: error.message
    });
  }
});

module.exports = router;