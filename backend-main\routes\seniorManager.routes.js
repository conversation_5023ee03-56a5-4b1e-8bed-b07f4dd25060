const express = require('express');
const router = express.Router();
const { protect, authorize } = require("../middleware/auth");

const {
  getSeniorManagerDashboardStats,
  getBudgetAnalytics,
  getAllProjects,
  getComprehensiveReports,
} = require("../controllers/seniorManager.controller");

// Dashboard routes
router.get("/dashboard/stats", protect, authorize('seniorManager'), (req, res, next) => {
  console.log("🎯 Senior manager dashboard route accessed by:", req.user?.email, "with role:", req.user?.role?.name);
  next();
}, getSeniorManagerDashboardStats);

// Budget analytics
router.get("/budget/analytics", protect, authorize('seniorManager'), getBudgetAnalytics);

// Projects overview
router.get("/projects", protect, authorize('seniorManager'), getAllProjects);

// Comprehensive reports
router.get("/reports", protect, authorize('seniorManager'), getComprehensiveReports);

module.exports = router;
