const mongoose = require("mongoose");

const NotificationSchema = new mongoose.Schema(
  {
    recipient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Notification recipient is required"],
    },
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      default: null, // System notifications won't have a sender
    },
    type: {
      type: String,
      enum: [
        // Project Manager notifications
        "project_created",
        "project_assigned",
        "team_assigned",
        "activity_report_submitted",
        "late_report_submission",

        // Senior Manager notifications
        "schedule_slippage",
        "budget_overuse",
        "budget_underuse",
        "project_manager_report_submitted",

        // Field Officer notifications
        "activity_assigned",
        "activity_due_reminder",
        "activity_overdue",
        "report_approved",
        "report_rejected",

        // Accountant notifications
        "budget_overuse_alert",
        "budget_underuse_alert",
        "activity_budget_assigned",

        // General notifications
        "budget_approved",
        "budget_rejected",
        "project_completion",
        "system_alert",
        "test_notification",
        "general"
      ],
      required: [true, "Notification type is required"],
    },
    title: {
      type: String,
      required: [true, "Notification title is required"],
    },
    message: {
      type: String,
      required: [true, "Notification message is required"],
    },
    read: {
      type: Boolean,
      default: false,
    },
    priority: {
      type: String,
      enum: ["low", "medium", "high", "urgent"],
      default: "medium",
    },
    relatedEntity: {
      entityType: {
        type: String,
        enum: ["project", "activity", "report", "budget", "user", "system"],
      },
      entityId: {
        type: mongoose.Schema.Types.ObjectId,
      },
    },
    actionUrl: {
      type: String, // URL to navigate to when notification is clicked
    },
    expiresAt: {
      type: Date,
      default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    },
  },
  { 
    timestamps: true,
    indexes: [
      { recipient: 1, read: 1, createdAt: -1 },
      { expiresAt: 1 }, // For TTL
    ]
  }
);

// TTL index to automatically delete expired notifications
NotificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

module.exports = mongoose.model("Notification", NotificationSchema);
