const express = require('express');
const router = express.Router();
const { protect, authorize } = require("../middleware/auth");
const multer = require('multer');

// Configure multer for memory storage (for email attachments)
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    console.log('📎 Multer fileFilter - File received:', file?.originalname, 'Type:', file?.mimetype);
    if (file && file.mimetype === 'application/pdf') {
      console.log('✅ PDF file accepted');
      cb(null, true);
    } else if (file) {
      console.log('❌ File rejected - not PDF');
      cb(new Error('Only PDF files are allowed'), false);
    } else {
      console.log('⚠️ No file provided to filter');
      cb(null, false);
    }
  }
});

const {
  getManagerDashboardStats,
  createActivity,
  getManagerActivities,
  getActivityReports,
  approveReport,
  rejectReport,
  submitProjectInsight,
  getProjectInsights,
  getBudgetOverview,
  sendReportToSeniorManager,
} = require("../controllers/manager.controller");

// Dashboard routes
router.get("/dashboard/stats", protect, authorize('projectManager'), getManagerDashboardStats);

// Activity management routes
router.post("/activities", protect, authorize('projectManager'), createActivity);
router.get("/activities", protect, authorize('projectManager'), getManagerActivities);

// Report management routes
router.get("/reports", protect, authorize('projectManager'), getActivityReports);
router.patch("/reports/:reportId/approve", protect, authorize('projectManager'), approveReport);
router.patch("/reports/:reportId/reject", protect, authorize('projectManager'), rejectReport);

// Project insights
router.post("/insights", protect, authorize('projectManager'), submitProjectInsight);
router.get("/insights", protect, authorize('projectManager'), getProjectInsights);

// Budget management
router.get("/budget/overview", protect, authorize('projectManager'), getBudgetOverview);



// Report management routes
router.post("/send-report-to-senior", protect, authorize('projectManager'), upload.single('report'), sendReportToSeniorManager);




module.exports = router;
