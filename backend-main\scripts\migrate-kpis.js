const mongoose = require('mongoose');
const path = require('path');

// Load environment variables from the parent directory
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

console.log('🔍 Checking environment variables...');
console.log('MONGO_URI exists:', !!process.env.MONGO_URI);

if (!process.env.MONGO_URI) {
  console.error('❌ MONGO_URI not found in environment variables');
  console.log('Please make sure your .env file contains MONGO_URI');
  process.exit(1);
}

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const ProjectActivities = require('../models/projectActivities.model');

async function migrateKPIs() {
  try {
    console.log('🔄 Starting KPIs migration...');
    
    // Find all activities that don't have the kpis field or have null/undefined kpis
    const activitiesWithoutKPIs = await ProjectActivities.find({
      $or: [
        { kpis: { $exists: false } },
        { kpis: null },
        { kpis: undefined }
      ]
    });
    
    console.log(`📊 Found ${activitiesWithoutKPIs.length} activities without KPIs field`);
    
    if (activitiesWithoutKPIs.length === 0) {
      console.log('✅ All activities already have KPIs field');
      return;
    }
    
    // Update all activities to have an empty kpis array
    const result = await ProjectActivities.updateMany(
      {
        $or: [
          { kpis: { $exists: false } },
          { kpis: null },
          { kpis: undefined }
        ]
      },
      {
        $set: { kpis: [] }
      }
    );
    
    console.log(`✅ Migration completed: ${result.modifiedCount} activities updated`);
    
    // Verify the migration
    const verifyCount = await ProjectActivities.countDocuments({ kpis: { $exists: true } });
    const totalCount = await ProjectActivities.countDocuments();
    
    console.log(`📊 Verification: ${verifyCount}/${totalCount} activities now have KPIs field`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the migration
migrateKPIs();
