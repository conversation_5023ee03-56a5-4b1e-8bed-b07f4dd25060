const Project = require("../models/project.model");
const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");
const User = require("../models/user.model");
const Team = require("../models/team.model");
const ActivityLogger = require("../utils/activityLogger");
const NotificationService = require("../services/notificationService");
const { sendDetailedActivityAssignmentMail } = require("../services/email.service");
const nodemailer = require("nodemailer");

// Helper function to update project budget and progress based on approved reports
const updateProjectBudgetAndProgress = async (projectId) => {
  try {
    // Get current project data
    const project = await Project.findById(projectId);
    if (!project) {
      console.error(`Project ${projectId} not found`);
      return;
    }

    // Get all activities for this project
    const projectActivities = await ProjectActivities.find({ project: projectId });
    const activityIds = projectActivities.map(a => a._id);

    // Get all approved reports for this project's activities
    const approvedReports = await ActivityReport.find({
      activity: { $in: activityIds },
      approved: true
    });

    // Calculate total spent from approved reports
    const newUsedBudget = approvedReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
    const newRemainingBudget = Math.max(project.initialBudget - newUsedBudget, 0);
    const newBudgetUtilization = project.initialBudget > 0 ?
      Math.round((newUsedBudget / project.initialBudget) * 100) : 0;

    // Calculate progress based on completed activities
    const totalActivities = projectActivities.length;
    const completedActivities = await ProjectActivities.countDocuments({
      project: projectId,
      status: 'completed'
    });
    const newProgressPercentage = totalActivities > 0 ?
      Math.round((completedActivities / totalActivities) * 100) : 0;

    // Update project
    await Project.findByIdAndUpdate(projectId, {
      usedBudget: newUsedBudget,
      remainingBudget: newRemainingBudget,
      budgetUtilization: Math.min(newBudgetUtilization, 100), // Cap at 100%
      progressPercentage: newProgressPercentage
    });

    console.log(`📊 Project ${projectId} updated:`);
    console.log(`   Used Budget: ${newUsedBudget} (calculated from ${approvedReports.length} approved reports)`);
    console.log(`   Remaining Budget: ${newRemainingBudget}`);
    console.log(`   Budget Utilization: ${newBudgetUtilization}%`);
    console.log(`   Progress: ${newProgressPercentage}%`);

  } catch (error) {
    console.error('Error updating project budget and progress:', error);
  }
};

// Get project manager dashboard statistics
exports.getManagerDashboardStats = async (req, res) => {
  try {
    const managerId = req.user.id;

    // Get projects assigned to this manager
    const assignedProjects = await Project.find({ assignedTo: managerId }).lean();
    const projectIds = assignedProjects.map(p => p._id);

    // Get all activities for projects assigned to this manager
    const totalActivities = await ProjectActivities.countDocuments({
      project: { $in: projectIds }
    });

    // Get in-progress activities for assigned projects
    const inProgressActivities = await ProjectActivities.countDocuments({
      project: { $in: projectIds },
      status: { $in: ['pending', 'in-progress'] }
    });

    // Get completed activities for assigned projects
    const completedActivities = await ProjectActivities.countDocuments({
      project: { $in: projectIds },
      status: 'completed'
    });

    // Get field officers assigned to activities in manager's projects
    const fieldOfficers = await ProjectActivities.distinct('assignedTo', {
      project: { $in: projectIds },
      assignedTo: { $exists: true }
    });
    const teamMembers = fieldOfficers.length;

    // Get recent activities for assigned projects with their reports
    const recentActivities = await ProjectActivities.find({
      project: { $in: projectIds }
    })
      .populate('project', 'title name')
      .populate('assignedTo', 'fullName')
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();

    // Get activity reports for these activities
    const activityIds = recentActivities.map(a => a._id);
    const activityReports = await ActivityReport.find({
      activity: { $in: activityIds }
    }).lean();

    // Combine activities with their reports
    const activitiesWithReports = recentActivities.map(activity => {
      const report = activityReports.find(r => r.activity.toString() === activity._id.toString());
      let actualOutcome = 'No report submitted';

      if (report && report.content) {
        // Extract the main description from content
        const descriptionContent = report.content.find(c => c.fieldName === 'description');
        if (descriptionContent) {
          actualOutcome = descriptionContent.entry;
        }
      }

      return {
        ...activity,
        actualOutcome,
        reportStatus: report ? (report.approved ? 'approved' : 'pending') : 'no_report',
        reportSubmitted: !!report
      };
    });

    res.status(200).json({
      status: "success",
      data: {
        totalActivities,
        inProgressActivities,
        completedActivities,
        teamMembers,
        assignedProjects: assignedProjects.length,
        recentActivities: activitiesWithReports,
        managerId: managerId,
        projects: assignedProjects.map(p => ({
          id: p._id,
          name: p.title || p.name
        }))
      }
    });

  } catch (error) {
    console.error("Manager dashboard stats error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch dashboard statistics"
    });
  }
};

// Create new activity
exports.createActivity = async (req, res) => {
  try {
    const {
      projectId,
      title,
      description,
      budget,
      assignedTo,
      startDate,
      endDate,
      location,
      expectedOutcome
    } = req.body;

    const managerId = req.user.id;

    console.log('🔍 Debug - Create Activity Request:');
    console.log('   User ID:', managerId);
    console.log('   User Name:', req.user.fullName);
    console.log('   User Role:', req.user.role?.name);
    console.log('   Project ID:', projectId);

    // Additional role check (should be handled by middleware, but adding for clarity)
    if (req.user.role?.name !== 'projectManager') {
      return res.status(403).json({
        status: "failed",
        message: `Access denied. Only project managers can create activities. Your role: ${req.user.role?.name}`
      });
    }

    // Verify project is assigned to this manager
    const project = await Project.findOne({
      _id: projectId,
      assignedTo: managerId
    });

    console.log('   Project found:', !!project);
    if (project) {
      console.log('   Project name:', project.name);
      console.log('   Project assignedTo:', project.assignedTo);
    }

    // Also check all projects assigned to this manager for debugging
    const allManagerProjects = await Project.find({ assignedTo: managerId });
    console.log('   Manager has', allManagerProjects.length, 'projects assigned');

    if (!project) {
      return res.status(403).json({
        status: "failed",
        message: "You can only create activities for projects assigned to you"
      });
    }

    // Create the activity
    console.log('🔧 Creating activity with data:', {
      project: projectId,
      title,
      description,
      budget,
      assignedTo,
      startDate,
      endDate,
      location,
      targetOutcome: expectedOutcome,
      createdBy: managerId,
      status: 'pending'
    });

    const activity = await ProjectActivities.create({
      project: projectId,
      title,
      description,
      budget,
      assignedTo,
      startDate,
      endDate,
      location,
      targetOutcome: expectedOutcome,
      createdBy: managerId,
      status: 'pending'
    });

    console.log('✅ Activity created successfully:', activity._id);

    // Populate the created activity
    await activity.populate([
      { path: 'project', select: 'name' },
      { path: 'assignedTo', select: 'fullName email' },
      { path: 'createdBy', select: 'fullName' }
    ]);

    // Send email notification to field officer
    try {
      if (assignedTo && activity.assignedTo) {
        await sendDetailedActivityAssignmentMail({
          to: activity.assignedTo.email,
          fieldOfficerName: activity.assignedTo.fullName,
          projectName: activity.project.name,
          activityTitle: activity.title,
          activityDescription: activity.description,
          startDate: activity.startDate,
          endDate: activity.endDate,
          budget: activity.budget,
          location: activity.location,
          targetOutcome: activity.targetOutcome,
          priority: activity.priority,
          assignedByName: activity.createdBy.fullName
        });
      }
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError);
      // Don't fail the activity creation if email fails
    }

    // Send in-app notification to field officer
    try {
      if (assignedTo) {
        await NotificationService.notifyActivityAssigned(activity._id);
        console.log('✅ Activity assignment notification sent to field officer');
      }
    } catch (notificationError) {
      console.error('❌ Failed to send activity assignment notification:', notificationError);
      // Don't fail the activity creation if notification fails
    }

    // Log the activity creation
    try {
      await ActivityLogger.activityCreated(
        managerId,
        activity._id,
        activity.title,
        project.name,
        req
      );
    } catch (logError) {
      console.error('Failed to log activity creation:', logError);
      // Don't fail the activity creation if logging fails
    }

    // Notify accountants about activity creation with budget
    try {
      if (budget && budget > 0) {
        const NotificationService = require('../services/notificationService');
        await NotificationService.notifyActivityBudgetAssignment(activity._id, managerId);
        console.log('✅ Activity budget creation notification sent to accountants');
      }
    } catch (notificationError) {
      console.error('❌ Failed to send activity budget creation notification:', notificationError);
      // Don't fail the activity creation if notification fails
    }

    res.status(201).json({
      status: "success",
      message: "Activity created successfully",
      data: activity
    });

  } catch (error) {
    console.error("Create activity error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to create activity"
    });
  }
};

// Get activities for projects assigned to manager
exports.getManagerActivities = async (req, res) => {
  try {
    const managerId = req.user.id;
    const { status, projectId } = req.query;

    // Get projects assigned to this manager
    const assignedProjects = await Project.find({ assignedTo: managerId }).select('_id title name');
    const projectIds = assignedProjects.map(p => p._id);

    console.log(`📊 Manager ${managerId} has ${assignedProjects.length} assigned projects:`,
      assignedProjects.map(p => ({ id: p._id, title: p.title || p.name })));

    let filter = { project: { $in: projectIds } };
    if (status) filter.status = status;
    if (projectId) {
      // If specific project is requested, make sure it's assigned to this manager
      if (projectIds.some(id => id.toString() === projectId)) {
        filter.project = projectId;
      } else {
        return res.status(403).json({
          status: "failed",
          message: "You can only view activities for projects assigned to you"
        });
      }
    }

    console.log(`🔍 Searching for activities with filter:`, filter);

    const activities = await ProjectActivities.find(filter)
      .populate('project', 'title name _id')
      .populate('assignedTo', 'fullName email')
      .populate('assignedBy', 'fullName email')
      .sort({ createdAt: -1 })
      .lean();

    console.log(`📊 Manager Activities Debug:`, {
      managerId,
      projectIds: projectIds.length,
      totalActivities: activities.length,
      activitiesWithKPIs: activities.filter(a => a.kpis && a.kpis.length > 0).length,
      sampleActivity: activities[0] ? {
        id: activities[0]._id,
        title: activities[0].title,
        kpis: activities[0].kpis || [],
        project: activities[0].project
      } : 'No activities'
    });

    res.status(200).json({
      status: "success",
      data: activities
    });

  } catch (error) {
    console.error("Get manager activities error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch activities"
    });
  }
};

// Get reports for manager's activities
exports.getActivityReports = async (req, res) => {
  try {
    const managerId = req.user.id;

    // Get reports submitted to this project manager
    const reports = await ActivityReport.find({
      submitTo: managerId
    })
      .populate('activity', 'title location')
      .populate({
        path: 'activity',
        populate: {
          path: 'project',
          select: 'title name location',
          populate: {
            path: 'location',
            select: 'name'
          }
        }
      })
      .populate('submittedBy', 'fullName email')
      .sort({ createdAt: -1 })
      .lean();

    // Transform the data to match the expected format
    const transformedReports = reports.map(report => {
      let status = 'pending';
      if (report.approved) {
        status = 'approved';
      } else if (report.rejectedBy) {
        status = 'rejected';
      }

      console.log('📊 Report amountSpent:', report.amountSpent, 'Type:', typeof report.amountSpent);

      // Get location from activity or project
      let location = 'N/A';
      if (report.activity?.location && report.activity.location.trim()) {
        location = report.activity.location;
      } else if (report.activity?.project?.location?.name) {
        location = report.activity.project.location.name;
      }

      return {
        _id: report._id,
        activity: report.activity,
        project: report.activity?.project ? {
          ...report.activity.project,
          name: report.activity.project.title || report.activity.project.name
        } : null,
        submittedBy: report.submittedBy,
        content: report.content,
        amountSpent: report.amountSpent || 0,
        status: status,
        createdAt: report.createdAt,
        reportDate: report.createdAt,
        location: location,
        rejectionReason: report.rejectionReason
      };
    });

    res.status(200).json({
      status: "success",
      data: transformedReports
    });

  } catch (error) {
    console.error("Get activity reports error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch activity reports"
    });
  }
};

// Approve activity report
exports.approveReport = async (req, res) => {
  try {
    const { reportId } = req.params;
    const managerId = req.user.id;

    const report = await ActivityReport.findById(reportId)
      .populate('activity')
      .populate({
        path: 'activity',
        populate: {
          path: 'project'
        }
      });

    if (!report) {
      return res.status(404).json({
        status: "failed",
        message: "Report not found"
      });
    }

    // Verify the report was submitted to this manager
    if (report.submitTo.toString() !== managerId) {
      return res.status(403).json({
        status: "failed",
        message: "You can only approve reports submitted to you"
      });
    }

    // Update report status
    report.approved = true;
    report.approvedBy = managerId;
    report.approvedAt = new Date();
    await report.save();

    // Update activity status to completed only after approval
    await ProjectActivities.findByIdAndUpdate(report.activity._id, {
      status: 'completed'
    });

    // Update project budget and progress
    await updateProjectBudgetAndProgress(report.activity.project._id);

    // Send notification to field officer about approval
    const NotificationService = require('../services/notificationService');
    await NotificationService.notifyReportApproved(reportId, managerId);

    // Trigger budget monitoring check for the project
    const BudgetMonitoringService = require('../services/budgetMonitoringService');
    await BudgetMonitoringService.checkProjectBudget(report.activity.project._id);

    // Check if project should be marked as completed after this activity approval
    await BudgetMonitoringService.monitorProjectProgress();

    console.log(`✅ Report ${reportId} approved by manager ${managerId}`);
    console.log(`✅ Activity ${report.activity._id} marked as completed`);
    console.log(`💰 Project budget updated with spent amount: ${report.amountSpent}`);

    res.status(200).json({
      status: "success",
      message: "Report approved successfully",
      data: report
    });

  } catch (error) {
    console.error("Approve report error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to approve report"
    });
  }
};

// Reject activity report
exports.rejectReport = async (req, res) => {
  try {
    const { reportId } = req.params;
    const { reason } = req.body;
    const managerId = req.user.id;

    if (!reason || reason.trim() === '') {
      return res.status(400).json({
        status: "failed",
        message: "Rejection reason is required"
      });
    }

    const report = await ActivityReport.findById(reportId)
      .populate('activity')
      .populate('project');

    if (!report) {
      return res.status(404).json({
        status: "failed",
        message: "Report not found"
      });
    }

    // Verify the report was submitted to this manager
    if (report.submitTo.toString() !== managerId) {
      return res.status(403).json({
        status: "failed",
        message: "You can only reject reports submitted to you"
      });
    }

    // Update report status
    report.approved = false;
    report.rejectedBy = managerId;
    report.rejectedAt = new Date();
    report.rejectionReason = reason;
    await report.save();

    // Update activity status back to in-progress
    await ProjectActivities.findByIdAndUpdate(report.activity._id, {
      status: 'in-progress'
    });

    // Send notification to field officer about rejection
    const NotificationService = require('../services/notificationService');
    await NotificationService.notifyReportRejected(reportId, managerId, reason);

    res.status(200).json({
      status: "success",
      message: "Report rejected successfully",
      data: report
    });

  } catch (error) {
    console.error("Reject report error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to reject report"
    });
  }
};

// Helper function to get role ID
async function getRoleId(roleName) {
  const Role = require("../models/roles.model");
  const role = await Role.findOne({ name: roleName });
  return role ? role._id : null;
}

// Submit project insights
exports.submitProjectInsight = async (req, res) => {
  try {
    const { projectId, lessons, recommendations } = req.body;
    const managerId = req.user.id;

    // Verify project is assigned to this manager
    const project = await Project.findOne({
      _id: projectId,
      assignedTo: managerId
    });

    if (!project) {
      return res.status(403).json({
        status: "failed",
        message: "You can only submit insights for projects assigned to you"
      });
    }

    // Create or update project insights
    const ProjectInsight = require("../models/projectInsight.model");

    const insight = await ProjectInsight.findOneAndUpdate(
      { project: projectId, submittedBy: managerId },
      {
        project: projectId,
        submittedBy: managerId,
        lessonsLearned: lessons,
        recommendations,
        submittedAt: new Date()
      },
      { upsert: true, new: true }
    );

    res.status(200).json({
      status: "success",
      message: "Project insight submitted successfully",
      data: insight
    });

  } catch (error) {
    console.error("Submit project insight error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to submit project insight"
    });
  }
};

// Get project insights for manager
exports.getProjectInsights = async (req, res) => {
  try {
    const managerId = req.user.id;

    // Get projects assigned to this manager
    const assignedProjects = await Project.find({ assignedTo: managerId }).select('_id');
    const projectIds = assignedProjects.map(p => p._id);

    // Get insights for these projects
    const ProjectInsight = require("../models/projectInsight.model");
    const insights = await ProjectInsight.find({
      project: { $in: projectIds }
    })
      .populate('project', 'title name')
      .populate('submittedBy', 'fullName')
      .sort({ submittedAt: -1 })
      .lean();

    res.status(200).json({
      status: "success",
      data: insights
    });

  } catch (error) {
    console.error("Get project insights error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch project insights"
    });
  }
};

// Get budget overview for manager
exports.getBudgetOverview = async (req, res) => {
  try {
    const managerId = req.user.id;

    // Get projects assigned to this manager
    const assignedProjects = await Project.find({ assignedTo: managerId });
    const projectIds = assignedProjects.map(p => p._id);

    // Get activities for projects assigned to this manager
    const activities = await ProjectActivities.find({ project: { $in: projectIds } });
    const activityIds = activities.map(a => a._id);

    // Get all approved activity reports for these activities
    const approvedReports = await ActivityReport.find({
      activity: { $in: activityIds },
      approved: true
    });

    // Calculate total spent from approved reports
    const totalSpentFromReports = approvedReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);

    // Calculate total budget from project data
    const totalBudget = assignedProjects.reduce((sum, project) => sum + (project.initialBudget || 0), 0);
    const totalRemaining = totalBudget - totalSpentFromReports;

    // Calculate total allocated to activities
    const totalAllocated = activities.reduce((sum, activity) => sum + (activity.budget || 0), 0);

    // Get budget distribution by project with actual spending from reports
    const budgetDistribution = await Promise.all(
      assignedProjects.map(async (project) => {
        const projectActivities = await ProjectActivities.find({
          project: project._id
        });

        const allocated = projectActivities.reduce((sum, activity) => sum + (activity.budget || 0), 0);

        // Get activity IDs for this project
        const projectActivityIds = projectActivities.map(a => a._id);

        // Get approved reports for this project's activities
        const projectReports = await ActivityReport.find({
          activity: { $in: projectActivityIds },
          approved: true
        });

        // Calculate actual spent amount from approved reports
        const actualSpent = projectReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
        const projectBudget = project.initialBudget || 0;
        const remaining = projectBudget - actualSpent;
        const utilization = projectBudget > 0 ? Math.round((actualSpent / projectBudget) * 100) : 0;

        return {
          id: project._id,
          name: project.title || project.name,
          budget: projectBudget,
          allocated,
          spent: actualSpent,
          remaining: Math.max(remaining, 0), // Ensure remaining is not negative
          utilization,
          progress: project.progressPercentage || 0
        };
      })
    );

    res.status(200).json({
      status: "success",
      data: {
        totalBudget,
        totalAllocated,
        totalSpent: totalSpentFromReports,
        totalRemaining: Math.max(totalRemaining, 0),
        budgetUtilization: totalBudget > 0 ? Math.round((totalSpentFromReports / totalBudget) * 100) : 0,
        budgetDistribution
      }
    });

  } catch (error) {
    console.error("Get budget overview error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch budget overview"
    });
  }
};



// Helper function to send email notification
async function sendActivityAssignmentEmail(activity) {
  try {
    // Configure nodemailer (you'll need to set up your email service)
    const transporter = nodemailer.createTransporter({
      // Add your email configuration here
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: activity.assignedTo.email,
      subject: `New Activity Assigned: ${activity.title}`,
      html: `
        <h2>New Activity Assignment</h2>
        <p>Hello ${activity.assignedTo.fullName},</p>
        <p>You have been assigned a new activity:</p>
        <ul>
          <li><strong>Activity:</strong> ${activity.title}</li>
          <li><strong>Project:</strong> ${activity.project.name}</li>
          <li><strong>Budget:</strong> MK ${activity.budget?.toLocaleString()}</li>
          <li><strong>Start Date:</strong> ${new Date(activity.startDate).toLocaleDateString()}</li>
          <li><strong>End Date:</strong> ${new Date(activity.endDate).toLocaleDateString()}</li>
        </ul>
        <p>Please log into the system to view full details and start working on this activity.</p>
        <p>Best regards,<br>Project Management System</p>
      `
    };

    await transporter.sendMail(mailOptions);
    console.log('Activity assignment email sent successfully');
  } catch (error) {
    console.error('Failed to send activity assignment email:', error);
  }
}

// Send project report to senior manager
exports.sendReportToSeniorManager = async (req, res) => {
  try {
    console.log('🔄 Send report to senior manager request received');
    console.log('📋 Request body:', req.body);
    console.log('📎 Request file:', req.file ? 'File received' : 'No file');

    const managerId = req.user.id;
    const { projectId, projectName } = req.body;

    console.log('👤 Manager ID:', managerId);
    console.log('📊 Project ID:', projectId);
    console.log('📝 Project Name:', projectName);

    // Get the uploaded PDF file
    const reportFile = req.file;

    if (!reportFile) {
      console.log('❌ No report file provided');

      // For testing purposes, allow requests without files
      if (req.body.testMode || !projectId || !projectName) {
        return res.status(400).json({
          status: "failed",
          message: "No report file provided. This endpoint requires a PDF file upload."
        });
      }

      // If we have project data but no file, return a test response
      return res.status(200).json({
        status: "success",
        message: "Test successful - endpoint is working. File upload would be processed here.",
        data: {
          projectId,
          projectName,
          note: "This is a test response. Actual implementation requires PDF file upload."
        }
      });
    }

    console.log('✅ Report file received:', reportFile.originalname, 'Size:', reportFile.size);

    // Get senior manager email
    console.log('🔍 Looking for senior manager...');
    let seniorManager = await User.findOne({
      'role.name': 'seniorManager'
    }).populate('role').select('email fullName role');

    if (!seniorManager) {
      console.log('❌ Senior manager not found with role.name, trying alternative query...');
      // Try alternative query
      seniorManager = await User.findOne()
        .populate('role')
        .where('role.name').equals('seniorManager')
        .select('email fullName role');

      if (!seniorManager) {
        console.log('❌ Senior manager not found with alternative query either');
        return res.status(404).json({
          status: "failed",
          message: "Senior manager not found"
        });
      }

      console.log('✅ Senior manager found with alternative query:', seniorManager.fullName);
    } else {
      console.log('✅ Senior manager found:', seniorManager.fullName);
    }

    console.log('✅ Senior manager found:', seniorManager.fullName);

    // Get project manager details
    console.log('🔍 Getting project manager details...');
    const projectManager = await User.findById(managerId).select('fullName email');

    if (!projectManager) {
      console.log('❌ Project manager not found');
      return res.status(404).json({
        status: "failed",
        message: "Project manager not found"
      });
    }

    console.log('✅ Project manager found:', projectManager.fullName);

    // Send email with attachment
    console.log('📧 Sending email to senior manager...');
    const emailService = require('../services/email.service');

    await emailService.sendProjectReportToSeniorManager({
      seniorManager,
      projectManager,
      projectName,
      reportFile
    });

    // Send notification to senior manager about report submission
    const NotificationService = require('../services/notificationService');
    await NotificationService.notifyProjectManagerReportSubmitted(projectId, managerId, "Project Report");

    console.log('✅ Email sent successfully');
    res.status(200).json({
      status: "success",
      message: "Project report sent to senior manager successfully"
    });

  } catch (error) {
    console.error("❌ Send report to senior manager error:", error);
    console.error("Error stack:", error.stack);

    // Return more specific error information
    const errorMessage = error.message || "Failed to send report to senior manager";
    const statusCode = error.name === 'ValidationError' ? 400 : 500;

    res.status(statusCode).json({
      status: "failed",
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
