const Notification = require("../models/notification.model");
const User = require("../models/user.model");
const Project = require("../models/project.model");
const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");
const WebSocketService = require("./websocketService");

class NotificationService {
  // Create a new notification
  static async createNotification({
    recipient,
    sender = null,
    type,
    title,
    message,
    priority = "medium",
    relatedEntity = {},
    actionUrl = null,
  }) {
    try {
      const notification = await Notification.create({
        recipient,
        sender,
        type,
        title,
        message,
        priority,
        relatedEntity,
        actionUrl,
      });

      // Send real-time notification (optional - don't fail if WebSocket fails)
      try {
        WebSocketService.notifyNewNotification(recipient, {
          _id: notification._id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          priority: notification.priority,
          createdAt: notification.createdAt,
          read: false
        });
      } catch (wsError) {
        console.warn('⚠️ WebSocket notification failed (non-critical):', wsError.message);
      }

      return notification;
    } catch (error) {
      throw error;
    }
  }

  // Send notification when project is created
  static async notifyProjectCreated(projectId, createdById) {
    try {
      const project = await Project.findById(projectId).populate("createdBy assignedTo");
      
      // Notify assigned project manager
      if (project.assignedTo) {
        await this.createNotification({
          recipient: project.assignedTo._id,
          sender: createdById,
          type: "project_assigned",
          title: "New Project Assigned",
          message: `You have been assigned to manage project: ${project.name}`,
          priority: "high",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/manager/projects/${projectId}`,
        });
      }

      // Notify accountant for budget approval
      const accountantRole = await require("../models/role.model").findOne({ name: "accountant" });
      const accountant = await User.findOne({ role: accountantRole._id });
      
      if (accountant) {
        await this.createNotification({
          recipient: accountant._id,
          sender: createdById,
          type: "project_created",
          title: "Budget Approval Required",
          message: `New project "${project.name}" requires budget approval of $${project.budget}`,
          priority: "high",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/accountant/projects/${projectId}`,
        });
      }
    } catch (error) {
      throw error;
    }
  }

  // Send notification when budget is approved
  static async notifyBudgetApproved(projectId, approvedById) {
    try {
      const project = await Project.findById(projectId).populate("createdBy");
      
      await this.createNotification({
        recipient: project.createdBy._id,
        sender: approvedById,
        type: "budget_approved",
        title: "Budget Approved",
        message: `Budget for project "${project.name}" has been approved`,
        priority: "medium",
        relatedEntity: {
          entityType: "project",
          entityId: projectId,
        },
        actionUrl: `/senior-manager/projects/${projectId}`,
      });
    } catch (error) {
      throw error;
    }
  }

  // Send notification when budget is rejected
  static async notifyBudgetRejected(projectId, rejectedById, reason) {
    try {
      const project = await Project.findById(projectId).populate("createdBy");
      
      await this.createNotification({
        recipient: project.createdBy._id,
        sender: rejectedById,
        type: "budget_rejected",
        title: "Budget Rejected",
        message: `Budget for project "${project.name}" has been rejected. Reason: ${reason}`,
        priority: "high",
        relatedEntity: {
          entityType: "project",
          entityId: projectId,
        },
        actionUrl: `/senior-manager/projects/${projectId}`,
      });
    } catch (error) {
      throw error;
    }
  }

  // Send notification when activity is assigned
  static async notifyActivityAssigned(activityId, assignedById) {
    try {
      const activity = await ProjectActivities.findById(activityId)
        .populate("assignedTo project");
      
      await this.createNotification({
        recipient: activity.assignedTo._id,
        sender: assignedById,
        type: "activity_assigned",
        title: "New Activity Assigned",
        message: `You have been assigned activity: ${activity.title} for project ${activity.project.name}`,
        priority: "medium",
        relatedEntity: {
          entityType: "activity",
          entityId: activityId,
        },
        actionUrl: `/field-officer/activities/${activityId}`,
      });
    } catch (error) {
      throw error;
    }
  }

  // Send notification when activity report is submitted
  static async notifyActivityReportSubmitted(reportId) {
    try {
      const report = await ActivityReport.findById(reportId)
        .populate({
          path: "activity",
          populate: {
            path: "project",
            populate: {
              path: "assignedTo"
            }
          }
        })
        .populate("submittedBy");
      
      if (report.activity.project.assignedTo) {
        await this.createNotification({
          recipient: report.activity.project.assignedTo._id,
          sender: report.submittedBy._id,
          type: "activity_report_submitted",
          title: "Activity Report Submitted",
          message: `${report.submittedBy.fullName} submitted a report for activity: ${report.activity.title}`,
          priority: "medium",
          relatedEntity: {
            entityType: "report",
            entityId: reportId,
          },
          actionUrl: `/manager/reports/${reportId}`,
        });
      }
    } catch (error) {
      throw error;
    }
  }

  // Send due date reminders (2 days before)
  static async sendDueDateReminders() {
    try {
      const twoDaysFromNow = new Date();
      twoDaysFromNow.setDate(twoDaysFromNow.getDate() + 2);
      twoDaysFromNow.setHours(23, 59, 59, 999);

      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      const upcomingActivities = await ProjectActivities.find({
        dueDate: {
          $gte: tomorrow,
          $lte: twoDaysFromNow,
        },
        status: { $ne: "completed" },
      }).populate("assignedTo project");

      for (const activity of upcomingActivities) {
        // Check if reminder already sent
        const existingReminder = await Notification.findOne({
          recipient: activity.assignedTo._id,
          type: "activity_due_reminder",
          "relatedEntity.entityId": activity._id,
          createdAt: {
            $gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        });

        if (!existingReminder) {
          const daysUntilDue = Math.ceil((new Date(activity.dueDate) - new Date()) / (1000 * 60 * 60 * 24));
          
          await this.createNotification({
            recipient: activity.assignedTo._id,
            type: "activity_due_reminder",
            title: "Activity Due Soon",
            message: `Activity "${activity.title}" is due in ${daysUntilDue} day(s)`,
            priority: "medium",
            relatedEntity: {
              entityType: "activity",
              entityId: activity._id,
            },
            actionUrl: `/field-officer/activities/${activity._id}`,
          });
        }
      }
    } catch (error) {
      throw error;
    }
  }

  // Flag late activity reports
  static async flagLateActivities() {
    try {
      const now = new Date();
      
      const overdueActivities = await ProjectActivities.find({
        dueDate: { $lt: now },
        status: { $ne: "completed" },
      }).populate("assignedTo project");

      for (const activity of overdueActivities) {
        // Check if report exists
        const report = await ActivityReport.findOne({ activity: activity._id });
        
        if (!report) {
          // Check if overdue notification already sent today
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          const existingNotification = await Notification.findOne({
            recipient: activity.assignedTo._id,
            type: "activity_overdue",
            "relatedEntity.entityId": activity._id,
            createdAt: { $gte: today },
          });

          if (!existingNotification) {
            const daysOverdue = Math.floor((now - new Date(activity.dueDate)) / (1000 * 60 * 60 * 24));
            
            // Notify field officer
            await this.createNotification({
              recipient: activity.assignedTo._id,
              type: "activity_overdue",
              title: "Activity Overdue",
              message: `Activity "${activity.title}" is ${daysOverdue} day(s) overdue`,
              priority: "urgent",
              relatedEntity: {
                entityType: "activity",
                entityId: activity._id,
              },
              actionUrl: `/field-officer/activities/${activity._id}`,
            });

            // Notify project manager
            if (activity.project.assignedTo) {
              await this.createNotification({
                recipient: activity.project.assignedTo,
                type: "activity_overdue",
                title: "Late Activity Report",
                message: `Activity "${activity.title}" report is ${daysOverdue} day(s) overdue`,
                priority: "high",
                relatedEntity: {
                  entityType: "activity",
                  entityId: activity._id,
                },
                actionUrl: `/manager/activities/${activity._id}`,
              });
            }
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }

  // Check for budget overuse
  static async checkBudgetOveruse() {
    try {
      const projects = await Project.find({
        status: "active",
      }).populate("assignedTo createdBy");

      for (const project of projects) {
        const usedBudget = project.budget - project.remainingBudget;
        const usagePercentage = (usedBudget / project.budget) * 100;

        // Alert if budget usage exceeds 90%
        if (usagePercentage > 90) {
          // Check if alert already sent today
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          const existingAlert = await Notification.findOne({
            type: "budget_overuse",
            "relatedEntity.entityId": project._id,
            createdAt: { $gte: today },
          });

          if (!existingAlert) {
            const recipients = [project.assignedTo, project.createdBy].filter(Boolean);
            
            for (const recipient of recipients) {
              await this.createNotification({
                recipient: recipient._id,
                type: "budget_overuse",
                title: "Budget Alert",
                message: `Project "${project.name}" has used ${usagePercentage.toFixed(1)}% of its budget`,
                priority: usagePercentage > 95 ? "urgent" : "high",
                relatedEntity: {
                  entityType: "project",
                  entityId: project._id,
                },
                actionUrl: `/manager/projects/${project._id}/budget`,
              });
            }
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }

  // Get notifications for a user
  static async getUserNotifications(userId, page = 1, limit = 20, unreadOnly = false) {
    try {
      console.log('📋 Getting notifications for user:', userId);
      console.log('📋 Query params:', { page, limit, unreadOnly });

      const query = { recipient: userId };
      if (unreadOnly) {
        query.read = false;
      }

      console.log('🔍 MongoDB query:', query);

      const notifications = await Notification.find(query)
        .populate("sender", "fullName email")
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

      const total = await Notification.countDocuments(query);
      const unreadCount = await Notification.countDocuments({
        recipient: userId,
        read: false,
      });

      console.log('✅ Found notifications:', notifications.length);
      console.log('📊 Total:', total, 'Unread:', unreadCount);

      return {
        notifications,
        total,
        unreadCount,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error('❌ Error in getUserNotifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  static async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOneAndUpdate(
        { _id: notificationId, recipient: userId },
        { read: true },
        { new: true }
      );
      return notification;
    } catch (error) {
      throw error;
    }
  }

  // Mark all notifications as read for a user
  static async markAllAsRead(userId) {
    try {
      await Notification.updateMany(
        { recipient: userId, read: false },
        { read: true }
      );
      return true;
    } catch (error) {
      throw error;
    }
  }

  // Notify about late report submission
  static async notifyLateReportSubmission(activityId) {
    try {
      const activity = await ProjectActivities.findById(activityId)
        .populate("assignedTo project")
        .populate({
          path: "project",
          populate: {
            path: "assignedTo"
          }
        });

      if (activity && activity.project.assignedTo) {
        await this.createNotification({
          recipient: activity.project.assignedTo._id,
          type: "late_report_submission",
          title: "Late Report Submission",
          message: `Activity "${activity.title}" report was submitted late by ${activity.assignedTo.fullName}`,
          priority: "high",
          relatedEntity: {
            entityType: "activity",
            entityId: activityId,
          },
          actionUrl: `/manager/reports`,
        });
      }
    } catch (error) {
      console.error("Error sending late report notification:", error);
    }
  }

  // Notify about schedule slippage
  static async notifyScheduleSlippage(projectId, delayDays) {
    try {
      const seniorManagers = await User.find({ 'role.name': 'seniorManager' });
      const project = await Project.findById(projectId);

      for (const manager of seniorManagers) {
        await this.createNotification({
          recipient: manager._id,
          type: "schedule_slippage",
          title: "Schedule Slippage Alert",
          message: `Project "${project.name}" is ${delayDays} days behind schedule`,
          priority: "high",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/senior-manager/projects/${projectId}`,
        });
      }
    } catch (error) {
      console.error("Error sending schedule slippage notification:", error);
    }
  }

  // Notify about budget overuse
  static async notifyBudgetOveruse(projectId, overuseAmount, overusePercentage) {
    try {
      // Get role IDs first
      const Role = require('../models/roles.model');
      const seniorManagerRole = await Role.findOne({ name: 'seniorManager' });
      const accountantRole = await Role.findOne({ name: 'accountant' });

      const seniorManagers = seniorManagerRole ? await User.find({ role: seniorManagerRole._id }) : [];
      const accountants = accountantRole ? await User.find({ role: accountantRole._id }) : [];
      const project = await Project.findById(projectId);

      const message = `Project "${project.title}" has exceeded budget by MWK ${overuseAmount.toLocaleString()} (${overusePercentage}%)`;

      // Notify senior managers
      for (const manager of seniorManagers) {
        await this.createNotification({
          recipient: manager._id,
          type: "budget_overuse_alert",
          title: "Budget Overuse Alert",
          message,
          priority: "urgent",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/senior-manager/projects/${projectId}`,
        });
      }

      // Notify accountants
      for (const accountant of accountants) {
        await this.createNotification({
          recipient: accountant._id,
          type: "budget_overuse_alert",
          title: "Budget Overuse Alert",
          message,
          priority: "urgent",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/accountant/projects/${projectId}`,
        });
      }

      console.log(`✅ Budget overuse notifications sent to ${seniorManagers.length} senior managers and ${accountants.length} accountants for project "${project.title}"`);
    } catch (error) {
      console.error("❌ Error sending budget overuse notification:", error);
      throw error;
    }
  }

  // Notify about budget underuse
  static async notifyBudgetUnderuse(projectId, underuseAmount, underusePercentage) {
    try {
      // Get role IDs first
      const Role = require('../models/roles.model');
      const seniorManagerRole = await Role.findOne({ name: 'seniorManager' });
      const accountantRole = await Role.findOne({ name: 'accountant' });

      const seniorManagers = seniorManagerRole ? await User.find({ role: seniorManagerRole._id }) : [];
      const accountants = accountantRole ? await User.find({ role: accountantRole._id }) : [];
      const project = await Project.findById(projectId);

      const message = `Project "${project.title}" is underutilizing budget by MWK ${underuseAmount.toLocaleString()} (${underusePercentage}%)`;

      // Notify senior managers
      for (const manager of seniorManagers) {
        await this.createNotification({
          recipient: manager._id,
          type: "budget_underuse_alert",
          title: "Budget Underuse Alert",
          message,
          priority: "medium",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/senior-manager/projects/${projectId}`,
        });
      }

      // Notify accountants
      for (const accountant of accountants) {
        await this.createNotification({
          recipient: accountant._id,
          type: "budget_underuse_alert",
          title: "Budget Underuse Alert",
          message,
          priority: "medium",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/accountant/projects/${projectId}`,
        });
      }
    } catch (error) {
      console.error("Error sending budget underuse notification:", error);
    }
  }

  // Notify senior manager about project manager report submission
  static async notifyProjectManagerReportSubmitted(projectId, projectManagerId, reportType = "Project Report") {
    try {
      const seniorManagers = await User.find({ 'role.name': 'seniorManager' });
      const project = await Project.findById(projectId);
      const projectManager = await User.findById(projectManagerId);

      for (const manager of seniorManagers) {
        await this.createNotification({
          recipient: manager._id,
          sender: projectManagerId,
          type: "project_manager_report_submitted",
          title: "Project Report Submitted",
          message: `${projectManager.fullName} submitted a ${reportType} for project "${project.name}"`,
          priority: "medium",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/senior-manager/reports`,
        });
      }
    } catch (error) {
      console.error("Error sending project manager report notification:", error);
    }
  }

  // Notify field officer about report approval
  static async notifyReportApproved(reportId, approvedById) {
    try {
      const report = await ActivityReport.findById(reportId)
        .populate("submittedBy activity");

      await this.createNotification({
        recipient: report.submittedBy._id,
        sender: approvedById,
        type: "report_approved",
        title: "Report Approved",
        message: `Your report for activity "${report.activity.title}" has been approved`,
        priority: "medium",
        relatedEntity: {
          entityType: "report",
          entityId: reportId,
        },
        actionUrl: `/field/reports/${reportId}`,
      });
    } catch (error) {
      console.error("Error sending report approval notification:", error);
    }
  }

  // Notify field officer about report rejection
  static async notifyReportRejected(reportId, rejectedById, rejectionReason) {
    try {
      const report = await ActivityReport.findById(reportId)
        .populate("submittedBy activity");

      await this.createNotification({
        recipient: report.submittedBy._id,
        sender: rejectedById,
        type: "report_rejected",
        title: "Report Rejected",
        message: `Your report for activity "${report.activity.title}" was rejected. Reason: ${rejectionReason}`,
        priority: "high",
        relatedEntity: {
          entityType: "report",
          entityId: reportId,
        },
        actionUrl: `/field/reports/${reportId}`,
      });
    } catch (error) {
      console.error("Error sending report rejection notification:", error);
    }
  }

  // Mark all notifications as read for a user
  static async markAllAsRead(userId) {
    try {
      await Notification.updateMany(
        { recipient: userId, read: false },
        { read: true }
      );
      return true;
    } catch (error) {
      throw error;
    }
  }

  // Get unread notification count for a user
  static async getUnreadCount(userId) {
    try {
      const count = await Notification.countDocuments({
        recipient: userId,
        read: false
      });
      return count;
    } catch (error) {
      throw error;
    }
  }

  // Delete notification
  static async deleteNotification(notificationId, userId) {
    try {
      const notification = await Notification.findOneAndDelete({
        _id: notificationId,
        recipient: userId
      });
      return notification;
    } catch (error) {
      throw error;
    }
  }

  // Add missing notification method for project completion
  static async notifyProjectCompletion(projectId) {
    try {
      const project = await Project.findById(projectId).populate('assignedTo');

      // Get role IDs first
      const Role = require('../models/roles.model');
      const seniorManagerRole = await Role.findOne({ name: 'seniorManager' });
      const accountantRole = await Role.findOne({ name: 'accountant' });

      const seniorManagers = seniorManagerRole ? await User.find({ role: seniorManagerRole._id }) : [];
      const accountants = accountantRole ? await User.find({ role: accountantRole._id }) : [];

      // Notify project manager
      if (project.assignedTo) {
        await this.createNotification({
          recipient: project.assignedTo._id,
          type: "project_completion",
          title: "Project Completed",
          message: `Congratulations! Project "${project.title}" has been completed successfully`,
          priority: "medium",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/manager/projects/${projectId}`,
        });
      }

      // Notify senior managers
      for (const manager of seniorManagers) {
        await this.createNotification({
          recipient: manager._id,
          type: "project_completion",
          title: "Project Completed",
          message: `Project "${project.title}" has been completed successfully`,
          priority: "medium",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/senior-manager/projects/${projectId}`,
        });
      }

      // Notify accountants
      for (const accountant of accountants) {
        await this.createNotification({
          recipient: accountant._id,
          type: "project_completion",
          title: "Project Completed",
          message: `Project "${project.title}" has been completed successfully. Final budget utilization: MWK ${project.usedBudget?.toLocaleString() || 0} of MWK ${project.initialBudget?.toLocaleString() || 0}`,
          priority: "medium",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/accountant/projects/${projectId}`,
        });
      }
    } catch (error) {
      console.error("Error sending project completion notification:", error);
    }
  }

  // Notify accountants when activity is assigned with budget
  static async notifyActivityBudgetAssignment(activityId, assignedById) {
    try {
      const activity = await ProjectActivities.findById(activityId)
        .populate("project assignedTo assignedBy");

      if (!activity || !activity.budget || activity.budget <= 0) {
        console.log('⚠️ No budget assigned to activity, skipping accountant notification');
        return;
      }

      // Get accountant role ID first
      const Role = require('../models/roles.model');
      const accountantRole = await Role.findOne({ name: 'accountant' });
      const accountants = accountantRole ? await User.find({ role: accountantRole._id }) : [];

      for (const accountant of accountants) {
        await this.createNotification({
          recipient: accountant._id,
          sender: assignedById,
          type: "activity_budget_assigned",
          title: "Activity Budget Assigned",
          message: `Activity "${activity.title}" has been assigned with budget MWK ${activity.budget.toLocaleString()} for project "${activity.project.title}"`,
          priority: "medium",
          relatedEntity: {
            entityType: "activity",
            entityId: activityId,
          },
          actionUrl: `/accountant/activities/${activityId}`,
        });
      }

      console.log(`✅ Activity budget assignment notifications sent to ${accountants.length} accountants`);
    } catch (error) {
      console.error("Error sending activity budget assignment notification:", error);
    }
  }

  // Notify accountants when project is created
  static async notifyProjectCreation(projectId, createdById) {
    try {
      const project = await Project.findById(projectId).populate('createdBy');

      // Get accountant role ID first
      const Role = require('../models/roles.model');
      const accountantRole = await Role.findOne({ name: 'accountant' });
      const accountants = accountantRole ? await User.find({ role: accountantRole._id }) : [];

      for (const accountant of accountants) {
        await this.createNotification({
          recipient: accountant._id,
          sender: createdById,
          type: "project_created",
          title: "New Project Created",
          message: `New project "${project.title}" has been created with initial budget MWK ${project.initialBudget?.toLocaleString() || 0}`,
          priority: "medium",
          relatedEntity: {
            entityType: "project",
            entityId: projectId,
          },
          actionUrl: `/accountant/projects/${projectId}`,
        });
      }

      console.log(`✅ Project creation notifications sent to ${accountants.length} accountants`);
    } catch (error) {
      console.error("Error sending project creation notification:", error);
    }
  }

  // Notify project manager about deliverable submission
  static async notifyDeliverableSubmission(managerId, projectId, deliverableTitle, submitterName) {
    try {
      const project = await Project.findById(projectId).select('title name');

      await this.createNotification({
        recipient: managerId,
        type: "deliverable_submission",
        title: "New Deliverable Submission",
        message: `${submitterName} has submitted files for deliverable "${deliverableTitle}" in project "${project.title || project.name}".`,
        priority: "medium",
        relatedEntity: {
          entityType: "project",
          entityId: projectId,
          deliverableTitle: deliverableTitle
        },
        actionUrl: `/manager/deliverable-reviews`
      });

      console.log(`✅ Deliverable submission notification sent to manager ${managerId}`);
    } catch (error) {
      console.error("Error sending deliverable submission notification:", error);
    }
  }

  // Notify submitter about deliverable review
  static async notifyDeliverableReview(submitterId, projectId, deliverableTitle, action, reviewComments) {
    try {
      const project = await Project.findById(projectId).select('title name');

      let title, message, priority;

      switch (action) {
        case 'approve':
          title = "Deliverable Approved";
          message = `Your deliverable "${deliverableTitle}" for project "${project.title || project.name}" has been approved.`;
          priority = "medium";
          break;
        case 'reject':
          title = "Deliverable Rejected";
          message = `Your deliverable "${deliverableTitle}" for project "${project.title || project.name}" has been rejected. ${reviewComments ? 'Reason: ' + reviewComments : ''}`;
          priority = "high";
          break;
        case 'request_revision':
          title = "Deliverable Revision Required";
          message = `Your deliverable "${deliverableTitle}" for project "${project.title || project.name}" requires revision. ${reviewComments ? 'Comments: ' + reviewComments : ''}`;
          priority = "medium";
          break;
        default:
          return;
      }

      await this.createNotification({
        recipient: submitterId,
        type: "deliverable_review",
        title: title,
        message: message,
        priority: priority,
        relatedEntity: {
          entityType: "project",
          entityId: projectId,
          deliverableTitle: deliverableTitle,
          reviewAction: action
        },
        actionUrl: `/manager/deliverables`
      });

      console.log(`✅ Deliverable review notification sent to submitter ${submitterId}`);
    } catch (error) {
      console.error("Error sending deliverable review notification:", error);
    }
  }
}

module.exports = NotificationService;
