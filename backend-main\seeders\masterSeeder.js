const mongoose = require("mongoose");
require("dotenv").config();

// Import seeders
const { addRoles } = require("./rolesSeeder");
const { addDistricts } = require("./districtSeeder");
const { createAdmin } = require("./adminSeeder");


async function runAllSeeders() {
  try {
    console.log("🌱 Starting database seeding...");
    
    // Connect to MongoDB
    await mongoose.connect("mongodb://127.0.0.1:27017/sprodeta");
    console.log("✅ Connected to MongoDB");
    
    // Run seeders in order
    console.log("\n📋 Seeding roles...");
    await addRoles();
    
    console.log("\n🗺️ Seeding districts...");
    await addDistricts();
    
    console.log("\n👤 Seeding admin user...");
    await createAdmin();

  

    console.log("\n✅ All seeders completed successfully!");
    
  } catch (error) {
    console.error("❌ Error running seeders:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

// Run if this file is executed directly
if (require.main === module) {
  runAllSeeders();
}

module.exports = { runAllSeeders }; 