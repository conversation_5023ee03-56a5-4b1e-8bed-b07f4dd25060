{"version": 3, "file": "aggregation_cursor.js", "sourceRoot": "", "sources": ["../../src/cursor/aggregation_cursor.ts"], "names": [], "mappings": ";;;AACA,oCAAyC;AACzC,wCAMoB;AAGpB,uDAAoF;AACpF,uEAAmE;AAGnE,oCAA+D;AAC/D,uDAI2B;AAK3B;;;;;;GAMG;AACH,MAAa,iBAAiC,SAAQ,2BAA0B;IAK9E,gBAAgB;IAChB,YACE,MAAmB,EACnB,SAA2B,EAC3B,WAAuB,EAAE,EACzB,UAAwC,EAAE;QAE1C,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAElC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAEhC,MAAM,SAAS,GAAyB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAEhF,IACE,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI;YACpC,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,mCAAiB,CAAC,SAAS;YAC9D,CAAC,SAAS,EAAE,MAAM,IAAI,IAAI,IAAI,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC;YAEtD,MAAM,IAAI,qBAAa,CAAC,4DAA4D,CAAC,CAAC;IAC1F,CAAC;IAED,KAAK;QACH,MAAM,aAAa,GAAG,IAAA,oBAAY,EAAC,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9D,OAAO,aAAa,CAAC,OAAO,CAAC;QAC7B,OAAO,IAAI,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE;YACvE,GAAG,aAAa;SACjB,CAAC,CAAC;IACL,CAAC;IAEQ,GAAG,CAAI,SAA8B;QAC5C,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAyB,CAAC;IACtD,CAAC;IAED,gBAAgB;IAChB,KAAK,CAAC,WAAW,CAAC,OAAsB;QACtC,MAAM,OAAO,GAAG;YACd,GAAG,IAAI,CAAC,gBAAgB;YACxB,GAAG,IAAI,CAAC,aAAa;YACrB,OAAO;YACP,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC;QACF,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC;gBACH,IAAA,uCAA6B,EAAC,OAAO,EAAE,iBAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;YACvE,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,IAAI,qBAAa,CACrB,qFAAqF,CACtF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAI,8BAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE1F,MAAM,QAAQ,GAAG,MAAM,IAAA,oCAAgB,EAAC,IAAI,CAAC,MAAM,EAAE,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAE9F,OAAO,EAAE,MAAM,EAAE,kBAAkB,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAClE,CAAC;IAUD,KAAK,CAAC,OAAO,CACX,SAAiF,EACjF,OAAgC;QAEhC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACnF,OAAO,CACL,MAAM,IAAA,oCAAgB,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,8BAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE;YACpD,GAAG,IAAI,CAAC,gBAAgB,EAAE,uDAAuD;YACjF,GAAG,IAAI,CAAC,aAAa;YACrB,GAAG,OAAO;YACV,OAAO,EAAE,OAAO,IAAI,IAAI;SACzB,CAAC,CACH,CACF,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACvC,CAAC;IAgBD,QAAQ,CAAe,KAAe;QACpC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IACE,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI;YACpC,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,mCAAiB,CAAC,SAAS;YAC9D,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,EAC5C,CAAC;YACD,MAAM,IAAI,qBAAa,CAAC,4DAA4D,CAAC,CAAC;QACxF,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,OAAO,IAAuC,CAAC;IACjD,CAAC;IAID,KAAK,CAAC,MAAgB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IACnC,CAAC;IAED,oDAAoD;IACpD,KAAK,CAAC,MAAc;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IACnC,CAAC;IAED,oDAAoD;IACpD,KAAK,CAAC,MAAgB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IACnC,CAAC;IAED,mDAAmD;IACnD,GAAG,CAAC,IAA2C;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAwCG;IACH,OAAO,CAAgC,QAAkB;QACvD,OAAO,IAAI,CAAC,QAAQ,CAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,qDAAqD;IACrD,MAAM,CAAC,OAAiB;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,qDAAqD;IACrD,MAAM,CAAC,OAAiB;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,mDAAmD;IACnD,IAAI,CAAC,KAAa;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IAClC,CAAC;IAED,mDAAmD;IACnD,IAAI,CAAC,KAAW;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IAClC,CAAC;IAED,qDAAqD;IACrD,MAAM,CAAC,OAA0B;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,sDAAsD;IACtD,OAAO,CAAC,QAAkB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;IACrC,CAAC;CACF;AApND,8CAoNC"}