const express = require('express');
const router = express.Router();
const { protect } = require("../middleware/auth"); 
console.log("protect is", typeof protect); 

const {
    getDashboardStats,
  getSeniorManagerStats,
  getFieldOfficerStats,
    getProjectManagerStats,
  getAccountantStats,
  getBudgetAnalytics,
} = require("../controllers/dashboard.controller");


router.get("/stats/senior-manager", protect, getSeniorManagerStats);
router.get("/stats/field-officer", protect, getFieldOfficerStats);
router.get("/stats/project-manager", protect, getProjectManagerStats);
router.get('/stats/accountant', getAccountantStats);
router.get('/budget-analytics', protect, getBudgetAnalytics);


module.exports = router;
