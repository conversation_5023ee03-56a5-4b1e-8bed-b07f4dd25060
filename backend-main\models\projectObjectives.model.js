const mongoose = require("mongoose");

const ProjectObjectives = new mongoose.Schema(
  {
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Project",
      required: [true, "Please provide the project reference"],
    },
    description: {
      type: String,
      required: [true, "Please provide the objective description"],
      trim: true,
      minlength: [5, "Objective description must be at least 5 characters long"],
      maxlength: [500, "Objective description cannot exceed 500 characters"],
    },
    priority: {
      type: String,
      enum: ["high", "medium", "low"],
      default: "medium",
    },
    status: {
      type: String,
      enum: ["pending", "inprogress", "completed"],
      default: "pending",
    },
    targetDate: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide the user who created this objective"],
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better query performance
ProjectObjectives.index({ project: 1 });
ProjectObjectives.index({ status: 1 });
ProjectObjectives.index({ priority: 1 });
ProjectObjectives.index({ createdBy: 1 });

module.exports = mongoose.model("ProjectObjective", ProjectObjectives, "projectobjectives");
