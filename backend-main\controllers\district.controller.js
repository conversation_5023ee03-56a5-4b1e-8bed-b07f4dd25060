const District = require("../models/districts.model");

exports.addDistrict = async (req, res) => {
  try {
    const { name } = req.body;

    const existingDistrict = await District.findOne({ name });
    if (existingDistrict) {
      return res
        .status(400)
        .json({ status: "failed", message: "District already exist" });
    }

    await District.create({ name });

    res
      .status(201)
      .json({ status: "success", message: "District added successfully!!" });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.getAllDistricts = async (req, res) => {
  try {
    const districts = await District.find({});
    res.status(200).json({ status: "sucess", districts });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.deleteDistrict = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedDistrict = await District.findByIdAndDelete(id);

    if (!deletedDistrict) {
      return res
        .status(404)
        .json({ status: "failed", message: "District not found!!" });
    }

    res
      .status(200)
      .json({ status: "success", message: "District deleted successfully!!" });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.updateDistrict = async (req, res) => {
  try {
    const { id } = req.params;

    const { name } = req.body;

    const existingDistrict = await District.findOne({ name });
    if (existingDistrict) {
      return res
        .status(400)
        .json({ status: "failed", message: "District already exist" });
    }

    const updatedDistrict = await District.findByIdAndUpdate(
      id,
      { name },
      { $new: true }
    );

    if (!updatedDistrict) {
      return res
        .status(404)
        .json({ status: "failed", message: "District not found!!" });
    }

    res
      .status(200)
      .json({ status: "success", message: "District updated successfully!!" });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};
