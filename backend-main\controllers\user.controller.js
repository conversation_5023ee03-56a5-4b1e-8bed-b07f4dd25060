const User = require("../models/user.model");
const Role = require("../models/roles.model");
const { generatePassword } = require("../utils/generatePassword");
const { sendUserAddedMail } = require("../services/email.service");
const ActivityLogger = require("../utils/activityLogger");

exports.addUser = async (req, res) => {
  try {
    const { email, phoneNumber, role } = req.body;
    console.log(req.body);
    const existingEmail = await User.findOne({ email });
    if (existingEmail) {
      return res
        .status(400)
        .json({ status: "failed", message: "Email already exists" });
    }

    const existingPhonenumber = await User.findOne({ phoneNumber });
    if (existingPhonenumber) {
      return res
        .status(400)
        .json({ status: "failed", message: "Phone number already exists" });
    }

    const password = generatePassword();

    const newUser = await User.create({ ...req.body, password });

    const roleName = await Role.findById(role).select("name");

    console.log(roleName);

    await sendUserAddedMail(email, roleName?.name, password);

    // Log the user creation activity
    await ActivityLogger.userCreated(
      req.user?.id || 'system',
      newUser._id,
      newUser.fullName,
      newUser.email,
      roleName?.name || 'Unknown',
      req
    );

    res
      .status(201)
      .json({ status: "success", message: "User created successfully!" });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

exports.getAllUsers = async (req, res) => {
  try {
    // Clean up offline users (users inactive for more than 30 minutes)
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    await User.updateMany(
      {
        isOnline: true,
        lastSeen: { $lt: thirtyMinutesAgo }
      },
      {
        isOnline: false
      }
    );

    const { role } = req.query;
    console.log("Role:", role);
    let users;
    if (!role) {
      users = await User.find({})
        .select("-password -sessionToken")
        .populate("role", "name")
        .populate("district", "name");
    } else {
      const role_ = await Role.findOne({ name: role });
      console.log("Role:", role_);
      if (!role_) {
        return res.status(404).json({ message: "Role not found" });
      }
      users = await User.find({ role: role_?._id })
        .select("-password -sessionToken")
        .populate("role", "name")
        .populate("district", "name");
    }

    // Debug: Log the first user to see what fields are included
    if (users.length > 0) {
      console.log("📊 Sample user data:", {
        id: users[0]._id,
        fullName: users[0].fullName,
        isOnline: users[0].isOnline,
        lastSeen: users[0].lastSeen,
        lastLogin: users[0].lastLogin
      });
    }

    // console.log("Users:", users);

    res.status(200).json({ status: "success", users });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

exports.getUsers = async (req, res) => {
  try {
    const { role } = req.query;
    let query = {};

    if (role) {
      // Find role document by name
      const roleDoc = await Role.findOne({ name: role });
      if (!roleDoc) {
        return res.status(404).json({ message: "Role not found" });
      }
      query.role = roleDoc._id;
    }

    // Populate role to get role name if needed
    const users = await User.find(query)
      .select("_id fullName role")
      .populate("role", "name");

    // Map users to include role name if you want
    const usersWithRoleName = users.map((user) => ({
      _id: user._id,
      fullName: user.fullName,
      role: user.role?.name || null,
    }));

    res.status(200).json(usersWithRoleName);
  } catch (err) {
    res.status(500).json({ message: "Error fetching users" });
  }
};

exports.updateProfile = async (req, res) => {
  try {
    // Get user ID from authenticated user
    const userId = req.user.id;

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "User not found",
      });
    }

    // Map frontend field names to backend field names
    const updateData = {};

    // Handle name field (frontend sends 'name', backend expects 'fullName')
    if (req.body.name) updateData.fullName = req.body.name;
    if (req.body.fullName) updateData.fullName = req.body.fullName;

    // Handle phone field (frontend sends 'phone', backend expects 'phoneNumber')
    if (req.body.phone) updateData.phoneNumber = req.body.phone;
    if (req.body.phoneNumber) updateData.phoneNumber = req.body.phoneNumber;

    // Handle email
    if (req.body.email) {
      // Check if email is already taken by another user
      const existingUser = await User.findOne({
        email: req.body.email,
        _id: { $ne: userId }
      });
      if (existingUser) {
        return res.status(400).json({
          status: "failed",
          message: "Email is already taken by another user"
        });
      }
      updateData.email = req.body.email;
    }

    // Handle other fields
    if (req.body.bio !== undefined) updateData.bio = req.body.bio;
    if (req.body.experience !== undefined) updateData.experience = req.body.experience;
    if (req.body.skills) updateData.skills = req.body.skills;

    // Handle location/district
    if (req.body.locationId) updateData.district = req.body.locationId;
    if (req.body.district) updateData.district = req.body.district;

    // Handle department
    if (req.body.departmentId) updateData.department = req.body.departmentId;
    if (req.body.department && typeof req.body.department === 'string' && req.body.department.length === 24) {
      updateData.department = req.body.department;
    }

    const updatedUser = await User.findByIdAndUpdate(userId, updateData, {
      new: true,
      runValidators: true,
    })
      .select("-password")
      .populate("role", "name")
      .populate("district", "name")
      .populate("department", "name");

    res.status(200).json({
      status: "success",
      message: "Profile updated successfully",
      data: updatedUser,
    });
  } catch (error) {
    console.error("Update profile error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

exports.getUserSettings = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select("settings");
    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "User not found"
      });
    }

    // Return default settings if none exist
    const settings = user.settings || {
      emailNotifications: true,
      theme: "light",
      language: "en",
      timezone: "UTC"
    };

    res.status(200).json({
      status: "success",
      data: settings
    });
  } catch (error) {
    console.error("Get user settings error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch user settings"
    });
  }
};

exports.updateUserSettings = async (req, res) => {
  try {
    const {
      emailNotifications,
      projectUpdates,
      activityReminders,
      reportNotifications,
      systemAlerts,
      theme,
      language,
      timezone
    } = req.body;

    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "User not found"
      });
    }

    // Update settings, preserving existing values
    user.settings = {
      ...user.settings,
      emailNotifications: emailNotifications !== undefined ? emailNotifications : user.settings?.emailNotifications || true,
      projectUpdates: projectUpdates !== undefined ? projectUpdates : user.settings?.projectUpdates || true,
      activityReminders: activityReminders !== undefined ? activityReminders : user.settings?.activityReminders || true,
      reportNotifications: reportNotifications !== undefined ? reportNotifications : user.settings?.reportNotifications || true,
      systemAlerts: systemAlerts !== undefined ? systemAlerts : user.settings?.systemAlerts || true,
      theme: theme || user.settings?.theme || "light",
      language: language || user.settings?.language || "en",
      timezone: timezone || user.settings?.timezone || "UTC"
    };

    await user.save();

    res.status(200).json({
      status: "success",
      message: "Settings updated successfully",
      data: user.settings,
    });
  } catch (error) {
    console.error("Update user settings error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to update settings",
    });
  }
};



// Change password
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        status: "failed",
        message: "Current password and new password are required"
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        status: "failed",
        message: "New password must be at least 6 characters long"
      });
    }

    // Get user with password field
    const user = await User.findById(userId).select('+password');
    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "User not found"
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await user.matchPassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        status: "failed",
        message: "Current password is incorrect"
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    res.status(200).json({
      status: "success",
      message: "Password changed successfully"
    });
  } catch (error) {
    console.error("Change password error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to change password"
    });
  }
};

exports.changePassword = async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  try {
    const user = await User.findById(req.user.id).select("+password");
    if (!user) return res.status(404).json({ message: "User not found" });

    const isMatch = await user.matchPassword(currentPassword);
    if (!isMatch)
      return res.status(400).json({ message: "Incorrect password" });

    user.password = newPassword;
    await user.save();
    res.status(200).json({ message: "Password changed" });
  } catch (err) {
    res.status(500).json({ message: "Error changing password" });
  }
};

exports.getMe = async (req, res) => {
  try {
    // Get user ID from authenticated user
    const userId = req.user.id;

    const user = await User.findById(userId)
      .select("-password")
      .populate("role", "name")
      .populate("district", "name")
      .populate("department", "name");

    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "User not found",
      });
    }

    res.status(200).json({
      status: "success",
      data: user,
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

// Admin function to update any user
exports.updateUser = async (req, res) => {
  try {
    const { userId } = req.params;
    const updateData = req.body;

    // Get the current user data for logging
    const currentUser = await User.findById(userId).populate('role', 'name');
    if (!currentUser) {
      return res.status(404).json({
        status: "failed",
        message: "User not found"
      });
    }

    // Check if email is being changed and if it's already taken
    if (updateData.email && updateData.email !== currentUser.email) {
      const existingUser = await User.findOne({
        email: updateData.email,
        _id: { $ne: userId }
      });
      if (existingUser) {
        return res.status(400).json({
          status: "failed",
          message: "Email is already taken by another user"
        });
      }
    }

    // Update the user
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password').populate('role', 'name').populate('district', 'name');

    // Log the user update activity
    const changes = Object.keys(updateData).reduce((acc, key) => {
      if (currentUser[key] !== updateData[key]) {
        acc[key] = { from: currentUser[key], to: updateData[key] };
      }
      return acc;
    }, {});

    await ActivityLogger.userUpdated(
      req.user.id,
      userId,
      updatedUser.fullName,
      changes,
      req
    );

    res.status(200).json({
      status: "success",
      message: "User updated successfully",
      data: updatedUser
    });
  } catch (error) {
    console.error("Update user error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to update user"
    });
  }
};

// Admin function to delete a user
exports.deleteUser = async (req, res) => {
  try {
    const { userId } = req.params;

    // Get user data before deletion for logging
    const user = await User.findById(userId).populate('role', 'name');
    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "User not found"
      });
    }

    // Prevent admin from deleting themselves
    if (userId === req.user.id) {
      return res.status(400).json({
        status: "failed",
        message: "You cannot delete your own account"
      });
    }

    // Delete the user
    await User.findByIdAndDelete(userId);

    // Log the user deletion activity
    await ActivityLogger.userDeleted(
      req.user.id,
      userId,
      user.fullName,
      user.email,
      req
    );

    res.status(200).json({
      status: "success",
      message: "User deleted successfully"
    });
  } catch (error) {
    console.error("Delete user error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to delete user"
    });
  }
};

// Admin function to reset user password
exports.resetUserPassword = async (req, res) => {
  try {
    const { userId } = req.params;

    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "User not found"
      });
    }

    // Generate new password
    const newPassword = generatePassword();

    // Update user password
    user.password = newPassword;
    await user.save();

    // Send email with new password
    const role = await Role.findById(user.role).select("name");
    await sendUserAddedMail(user.email, role?.name || 'User', newPassword);

    // Log the password reset activity
    await ActivityLogger.passwordReset(
      req.user.id,
      userId,
      user.fullName,
      req
    );

    res.status(200).json({
      status: "success",
      message: "Password reset successfully. New password has been sent to user's email."
    });
  } catch (error) {
    console.error("Reset password error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to reset password"
    });
  }
};

// Get all field officers
exports.getFieldOfficers = async (req, res) => {
  try {
    console.log('👥 Fetching field officers...');

    // Try different possible role names for field officers
    const possibleRoleNames = ['fieldOfficer', 'field officer', 'Field Officer', 'fieldofficer'];
    let fieldOfficerRole = null;

    for (const roleName of possibleRoleNames) {
      fieldOfficerRole = await Role.findOne({ name: roleName });
      if (fieldOfficerRole) {
        console.log(`✅ Found field officer role with name: "${roleName}"`);
        break;
      }
    }

    if (!fieldOfficerRole) {
      console.log('❌ Field officer role not found with any of these names:', possibleRoleNames);

      // Get all available roles for debugging
      const allRoles = await Role.find().select('name');
      console.log('📋 Available roles:', allRoles.map(r => r.name));

      return res.status(404).json({
        status: "failed",
        message: "Field officer role not found",
        availableRoles: allRoles.map(r => r.name)
      });
    }

    console.log('🔍 Field officer role ID:', fieldOfficerRole._id);

    // Get all users with field officer role
    const fieldOfficers = await User.find({
      role: fieldOfficerRole._id
    })
    .select('fullName email phoneNumber district department createdAt')
    .populate('role', 'name')
    .populate('district', 'name')
    .populate('department', 'name')
    .sort({ fullName: 1 });

    console.log('👥 Found field officers:', fieldOfficers.length);

    res.status(200).json({
      status: "success",
      message: `Found ${fieldOfficers.length} field officers`,
      data: fieldOfficers
    });

  } catch (error) {
    console.error("❌ Get field officers error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching field officers",
      error: error.message
    });
  }
};

// Get users by role (generic function)
exports.getUsersByRole = async (req, res) => {
  try {
    const { roleName } = req.params;
    console.log(`👥 Fetching users with role: ${roleName}`);

    // Find the role
    const role = await Role.findOne({ name: roleName });

    if (!role) {
      return res.status(404).json({
        status: "failed",
        message: `Role '${roleName}' not found`
      });
    }

    // Get all users with this role
    const users = await User.find({
      role: role._id
    })
    .select('fullName email phoneNumber district department createdAt')
    .populate('role', 'name')
    .populate('district', 'name')
    .populate('department', 'name')
    .sort({ fullName: 1 });

    console.log(`👥 Found ${users.length} users with role ${roleName}`);

    res.status(200).json({
      status: "success",
      message: `Found ${users.length} users with role ${roleName}`,
      data: users
    });

  } catch (error) {
    console.error(`❌ Get users by role error:`, error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching users",
      error: error.message
    });
  }
};

// Get all available roles (for debugging)
exports.getAllRoles = async (req, res) => {
  try {
    console.log('📋 Fetching all roles...');

    const roles = await Role.find().select('name createdAt').sort({ name: 1 });

    console.log('📋 Found roles:', roles.map(r => r.name));

    res.status(200).json({
      status: "success",
      message: `Found ${roles.length} roles`,
      data: roles
    });

  } catch (error) {
    console.error("❌ Get roles error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching roles",
      error: error.message
    });
  }
};
