const mongoose = require('mongoose');
const Project = require('../models/project.model');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/project-management', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected for migration');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Migration function to update project statuses
const migrateProjectStatuses = async () => {
  try {
    console.log('🔄 Starting project status migration...');

    // Find all projects with old status values
    const projectsToUpdate = await Project.find({
      status: { $in: ['draft', 'planning', 'assigned', 'active', 'rejected'] }
    });

    console.log(`📊 Found ${projectsToUpdate.length} projects to migrate`);

    if (projectsToUpdate.length === 0) {
      console.log('✅ No projects need migration');
      return;
    }

    // Update projects based on their current status
    const updatePromises = projectsToUpdate.map(async (project) => {
      let newStatus = 'inprogress'; // Default to inprogress

      // Map old statuses to new ones
      switch (project.status) {
        case 'draft':
        case 'planning':
        case 'assigned':
        case 'active':
          newStatus = 'inprogress';
          break;
        case 'rejected':
          // For rejected projects, we'll set them as completed for now
          // You might want to handle this differently
          newStatus = 'completed';
          break;
        default:
          newStatus = 'inprogress';
      }

      console.log(`📝 Updating project "${project.title}" from "${project.status}" to "${newStatus}"`);

      return Project.findByIdAndUpdate(
        project._id,
        { status: newStatus },
        { new: true }
      );
    });

    // Execute all updates
    const updatedProjects = await Promise.all(updatePromises);

    console.log(`✅ Successfully migrated ${updatedProjects.length} projects`);

    // Show summary
    const statusCounts = await Project.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    console.log('📊 Current project status distribution:');
    statusCounts.forEach(({ _id, count }) => {
      console.log(`   ${_id}: ${count} projects`);
    });

  } catch (error) {
    console.error('❌ Migration error:', error);
    throw error;
  }
};

// Main migration function
const runMigration = async () => {
  try {
    await connectDB();
    await migrateProjectStatuses();
    console.log('🎉 Migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('📝 Database connection closed');
    process.exit(0);
  }
};

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

module.exports = { migrateProjectStatuses };
