const Project = require("../models/project.model");
const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");
const User = require("../models/user.model");
const ActivityLogger = require("../utils/activityLogger");

// Get accountant dashboard statistics
exports.getAccountantDashboardStats = async (req, res) => {
  try {
    // Get all projects for budget calculations
    const allProjects = await Project.find({}).lean();

    // Calculate total budget (all projects)
    const totalBudget = allProjects.reduce((sum, project) => sum + (project.initialBudget || 0), 0);

    // Get active/approved projects for monitoring (projects are created with 'inprogress' status)
    const approvedProjects = allProjects.filter(p =>
      p.status === 'inprogress' ||
      p.status === 'completed'
    );

    console.log(`Dashboard: Found ${allProjects.length} total projects, ${approvedProjects.length} active projects`);

    // Calculate budget utilization from activity reports
    const approvedReports = await ActivityReport.find({ approved: true });
    const totalUsed = approvedReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
    const budgetUtilization = totalBudget > 0 ? Math.round((totalUsed / totalBudget) * 100) : 0;

    // Count active projects
    const activeProjects = approvedProjects.length;

    // Get recent activity reports for the accountant
    const recentActivities = await ActivityReport.find({})
      .populate({
        path: 'activity',
        select: 'title project',
        populate: {
          path: 'project',
          select: 'name'
        }
      })
      .populate('submittedBy', 'fullName')
      .sort({ createdAt: -1 })
      .limit(5)
      .lean();

    // Get fund usage data for chart
    const fundUsageData = await Promise.all(
      approvedProjects.map(async (project) => {
        // Get project activities first, then find reports for those activities
        const projectActivities = await ProjectActivities.find({ project: project._id });
        const activityIds = projectActivities.map(activity => activity._id);

        const projectReports = await ActivityReport.find({
          activity: { $in: activityIds },
          approved: true
        });
        const used = projectReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);

        return {
          name: project.title, // Use title field directly
          approved: project.initialBudget || 0,
          used: used,
          remaining: (project.initialBudget || 0) - used,
          utilization: project.initialBudget > 0 ? Math.round((used / project.initialBudget) * 100) : 0
        };
      })
    );

    res.status(200).json({
      status: "success",
      data: {
        totalBudget,
        budgetUtilization,
        activeProjects,
        totalUsed,
        recentActivities,
        fundUsageData
      }
    });

  } catch (error) {
    console.error("Accountant dashboard stats error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch dashboard statistics"
    });
  }
};

// Get budget monitoring data for all projects
exports.getBudgetMonitoringData = async (req, res) => {
  try {
    // Get all projects (not just active ones) for budget monitoring
    const allProjects = await Project.find({})
      .populate('createdBy', 'fullName')
      .populate('assignedTo', 'fullName')
      .lean();

    console.log(`Found ${allProjects.length} total projects in database`);

    // Filter for projects that should be monitored (inprogress, completed)
    const projects = allProjects.filter(p =>
      p.status === 'inprogress' ||
      p.status === 'completed'
    );

    console.log(`Found ${projects.length} projects for budget monitoring`);
    if (projects.length > 0) {
      console.log('Project statuses:', projects.map(p => ({ name: p.name, status: p.status })));
    } else if (allProjects.length > 0) {
      console.log('No active projects found, but found these projects:',
        allProjects.map(p => ({ name: p.name, status: p.status })));
      // If no active projects but there are projects, include them for monitoring
      projects.push(...allProjects);
    }

    // Get all approved activity reports to calculate used budgets
    const activityReports = await ActivityReport.find({ approved: true })
      .populate('activity')
      .lean();

    // Calculate budget utilization for each project
    const projectBudgetData = await Promise.all(
      projects.map(async (project) => {
        // Calculate used budget from activity reports
        const projectReports = activityReports.filter(report =>
          report.activity && report.activity.project &&
          report.activity.project.toString() === project._id.toString()
        );

        const usedBudget = projectReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
        const remainingBudget = (project.initialBudget || 0) - usedBudget;
        const utilizationPercentage = project.initialBudget > 0 ? (usedBudget / project.initialBudget) * 100 : 0;

        // Determine status based on utilization
        let status = 'on-track';
        if (utilizationPercentage > 100) {
          status = 'over-budget';
        } else if (utilizationPercentage > 85) {
          status = 'at-risk';
        }

        // Get project activities count
        const totalActivities = await ProjectActivities.countDocuments({ project: project._id });
        const completedActivities = await ProjectActivities.countDocuments({
          project: project._id,
          status: 'completed'
        });

        // Get real monthly spending data
        const monthlySpending = await getRealMonthlySpending(project._id, projectReports);

        // Get real category breakdown data
        const categoryBreakdown = await getRealCategoryBreakdown(project._id, projectReports);

        return {
          id: project._id,
          name: project.title, // Use title field directly
          totalBudget: project.initialBudget || 0,
          usedBudget,
          remainingBudget,
          utilizationPercentage: Math.round(utilizationPercentage * 10) / 10,
          status,
          manager: project.assignedTo?.fullName || project.createdBy?.fullName || 'Unassigned',
          startDate: project.startDate,
          endDate: project.endDate,
          activities: totalActivities,
          completedActivities,
          monthlySpending,
          categoryBreakdown
        };
      })
    );

    res.status(200).json({
      status: "success",
      data: projectBudgetData
    });

  } catch (error) {
    console.error("Budget monitoring data error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch budget monitoring data"
    });
  }
};



// Helper function to get real monthly spending data
const getRealMonthlySpending = async (projectId, projectReports) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const currentYear = new Date().getFullYear();

  const monthlyData = months.map((month, index) => {
    // Filter reports for this month
    const monthReports = projectReports.filter(report => {
      const reportDate = new Date(report.createdAt);
      return reportDate.getMonth() === index && reportDate.getFullYear() === currentYear;
    });

    // Sum spending for this month
    const monthlyAmount = monthReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);

    return {
      month,
      amount: monthlyAmount
    };
  });

  return monthlyData;
};

// Helper function to get real category breakdown data
const getRealCategoryBreakdown = async (projectId, projectReports) => {
  // Get all project activities to analyze categories
  const projectActivities = await ProjectActivities.find({ project: projectId }).lean();

  // Initialize category totals
  const categoryTotals = {
    'Personnel': 0,
    'Equipment': 0,
    'Materials': 0,
    'Travel': 0,
    'Other': 0
  };

  // Categorize spending based on activity types or descriptions
  projectReports.forEach(report => {
    const amount = report.amountSpent || 0;
    const activity = projectActivities.find(act => act._id.toString() === report.activity.toString());

    if (activity) {
      // Simple categorization based on activity title/description
      const title = (activity.title || '').toLowerCase();
      const description = (activity.description || '').toLowerCase();

      if (title.includes('staff') || title.includes('salary') || title.includes('personnel') ||
          description.includes('staff') || description.includes('salary')) {
        categoryTotals['Personnel'] += amount;
      } else if (title.includes('equipment') || title.includes('tool') || title.includes('machine') ||
                 description.includes('equipment') || description.includes('tool')) {
        categoryTotals['Equipment'] += amount;
      } else if (title.includes('material') || title.includes('supply') || title.includes('resource') ||
                 description.includes('material') || description.includes('supply')) {
        categoryTotals['Materials'] += amount;
      } else if (title.includes('travel') || title.includes('transport') || title.includes('fuel') ||
                 description.includes('travel') || description.includes('transport')) {
        categoryTotals['Travel'] += amount;
      } else {
        categoryTotals['Other'] += amount;
      }
    } else {
      // If no activity found, categorize as Other
      categoryTotals['Other'] += amount;
    }
  });

  // Calculate total spending
  const totalSpending = Object.values(categoryTotals).reduce((sum, amount) => sum + amount, 0);

  // Convert to percentage-based breakdown
  return Object.entries(categoryTotals).map(([category, amount]) => ({
    category,
    amount,
    percentage: totalSpending > 0 ? Math.round((amount / totalSpending) * 100) : 0
  }));
};

// Debug endpoint to check what projects exist
exports.getProjectsDebug = async (req, res) => {
  try {
    const allProjects = await Project.find({})
      .select('title status initialBudget createdAt')
      .lean();

    const projectCount = await Project.countDocuments({});
    const statusCounts = await Project.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    res.status(200).json({
      status: "success",
      data: {
        totalProjects: projectCount,
        statusBreakdown: statusCounts,
        projects: allProjects
      }
    });
  } catch (error) {
    console.error("Debug projects error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch projects debug info"
    });
  }
};

// Get financial reports data
exports.getFinancialReports = async (req, res) => {
  try {
    // Get all projects with their financial data
    const projects = await Project.find({})
      .populate('createdBy', 'fullName')
      .populate('assignedTo', 'fullName')
      .lean();

    // Calculate financial data for each project
    const projectsWithFinancials = await Promise.all(
      projects.map(async (project) => {
        const activityReports = await ActivityReport.find({
          project: project._id,
          status: 'approved'
        });

        const totalSpent = activityReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
        const budgetUtilization = project.budget > 0 ? Math.round((totalSpent / project.budget) * 100) : 0;

        return {
          ...project,
          totalSpent,
          budgetUtilization,
          remaining: (project.budget || 0) - totalSpent,
          reportCount: activityReports.length
        };
      })
    );

    // Calculate overall statistics
    const totalBudget = projects.reduce((sum, p) => sum + (p.budget || 0), 0);
    const approvedBudget = projects.filter(p => p.status === 'active').reduce((sum, p) => sum + (p.budget || 0), 0);
    const totalSpent = projectsWithFinancials.reduce((sum, p) => sum + p.totalSpent, 0);
    const overallUtilization = approvedBudget > 0 ? Math.round((totalSpent / approvedBudget) * 100) : 0;

    res.status(200).json({
      status: "success",
      data: {
        projects: projectsWithFinancials,
        summary: {
          totalBudget,
          approvedBudget,
          totalSpent,
          overallUtilization,
          totalProjects: projects.length,
          activeProjects: projects.filter(p => p.status === 'active').length,
          completedProjects: projects.filter(p => p.status === 'completed').length
        }
      }
    });

  } catch (error) {
    console.error("Get financial reports error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch financial reports"
    });
  }
};
