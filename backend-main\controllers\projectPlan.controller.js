const ProjectGoal = require('../models/projectGoals.model');
const ProjectObjective = require('../models/projectObjectives.model');
const ProjectPlan = require('../models/projectPlan.model');

exports.createProjectGoal = async (req, res) => {
  try {
    const { projectId, description } = req.body;
    const goal = await ProjectGoal.create({ project: projectId, description });
    res.status(201).json({ success: true, data: goal });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.createObjective = async (req, res) => {
  try {
    const { goalId, description, isMandatory, targetDate } = req.body;
    const objective = await ProjectObjective.create({
      goal: goalId,
      description,
      isMandatory,
      targetDate,
    });
    res.status(201).json({ success: true, data: objective });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.getProjectPlan = async (req, res) => {
  try {
    const { projectId } = req.params;
    const goals = await ProjectGoal.find({ project: projectId }).lean();

    const populatedGoals = await Promise.all(
      goals.map(async (goal) => {
        const objectives = await ProjectObjective.find({ goal: goal._id }).lean();
        return { ...goal, objectives };
      })
    );

    res.status(200).json({ success: true, data: populatedGoals });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.createProjectPlan = async (req, res) => {
  try {
    const {
      project,
      objectives: objectivesData, // array of { title, goals: [{title, target}] }
      inputs,
      outputs,
      expectedOutcome,
    } = req.body;

    if (!project || !objectivesData || !expectedOutcome) {
      return res.status(400).json({
        success: false,
        message: "Missing required project plan fields.",
      });
    }

    // Array to hold all created goal and objective IDs
    const createdGoalIds = [];
    const createdObjectiveIds = [];

    // Create goals and objectives from nested data
    for (const obj of objectivesData) {
      for (const goalData of obj.goals) {
        // Create goal linked to project
        const createdGoal = await ProjectGoal.create({
          project,
          description: goalData.title || goalData.description || "No description",
        });
        createdGoalIds.push(createdGoal._id);

        // Create objective linked to goal
        const createdObjective = await ProjectObjective.create({
          goal: createdGoal._id,
          description: obj.title || obj.description || "No description",
          isMandatory: false,
          targetDate: null,
        });
        createdObjectiveIds.push(createdObjective._id);
      }
    }

    // Create ProjectPlan referencing the created goals and objectives
    const plan = await ProjectPlan.create({
      project,
      objectives: createdObjectiveIds,
      goals: createdGoalIds,
      inputs,
      outputs,
      expectedOutcome,
    });

    res.status(201).json({ success: true, data: plan });
  } catch (error) {
    console.error("Error creating project plan:", error);
    res.status(500).json({ success: false, message: error.message });
  }
};
 

const getProjectPlanByProjectId = async (req, res) => {
  try {
    const { projectId } = req.params;
    console.log('🔍 Fetching project plan for project ID:', projectId);

    const plan = await ProjectPlan.findOne({ project: projectId })
      .populate({
        path: 'objectives',
        select: 'description priority status targetDate'
      })
      .populate({
        path: 'goals',
        select: 'description priority status targetDate measurableOutcome'
      });

    console.log('📋 Found project plan:', plan ? 'Yes' : 'No');

    if (!plan) {
      console.log('❌ No project plan found for project:', projectId);
      return res.status(404).json({ message: 'Project plan not found' });
    }

    // Transform the data to match frontend expectations
    const transformedPlan = {
      ...plan.toObject(),
      objectives: plan.objectives?.map(obj => ({
        title: obj.description,
        description: obj.description,
        priority: obj.priority,
        status: obj.status,
        targetDate: obj.targetDate,
        goals: [] // Individual objectives don't have nested goals in this structure
      })) || [],
      goals: plan.goals?.map(goal => ({
        title: goal.description,
        description: goal.description,
        priority: goal.priority,
        status: goal.status,
        target: goal.measurableOutcome,
        targetDate: goal.targetDate
      })) || []
    };

    console.log('✅ Returning transformed project plan with objectives:', transformedPlan.objectives?.length || 0, 'goals:', transformedPlan.goals?.length || 0);
    console.log('📋 Expected outcome:', transformedPlan.expectedOutcome || 'Not set');
    console.log('📋 Inputs:', transformedPlan.inputs?.length || 0);
    console.log('📋 Outputs:', transformedPlan.outputs?.length || 0);

    res.status(200).json(transformedPlan);
  } catch (error) {
    console.error('❌ Error fetching project plan:', error);
    res.status(500).json({ message: 'Server error fetching project plan' });
  }
};

module.exports = {
  createProjectGoal: exports.createProjectGoal,
  createObjective: exports.createObjective,
  getProjectPlan: exports.getProjectPlan,
  createProjectPlan: exports.createProjectPlan,
  getProjectPlanByProjectId,
};

