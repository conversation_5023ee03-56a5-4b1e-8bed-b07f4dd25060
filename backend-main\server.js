const mongoose = require("mongoose");
const app = require("./app");
const http = require("http");
const WebSocketService = require("./services/websocketService");

const port = process.env.PORT || 7000;

mongoose
  .connect(process.env.DATABASE_URL)
  .then(() => {
    console.log("Database connected successfully!!");

    // Create HTTP server
    const server = http.createServer(app);

    // Initialize WebSocket service
    WebSocketService.init(server);

    server.listen(port, "0.0.0.0", () => {
      console.log(`Server listening on port ${port}`);
      console.log(`WebSocket server available at ws://localhost:${port}/ws`);
    });
  })
  .catch((error) => {
    console.log(error);
  });
