const User = require("../models/user.model");
const Role = require("../models/roles.model");
const Activity = require("../models/recentActivities.model");
const ActivityLogger = require("../utils/activityLogger");

// Get admin dashboard statistics
exports.getDashboardStats = async (req, res) => {
  try {
    // Clean up offline users (users inactive for more than 30 minutes)
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    await User.updateMany(
      {
        isOnline: true,
        lastSeen: { $lt: thirtyMinutesAgo }
      },
      {
        isOnline: false
      }
    );

    // Get user statistics
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: { $ne: false } });

    // Get new users today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const newUsersToday = await User.countDocuments({
      createdAt: { $gte: today }
    });

    // Get total roles
    const totalRoles = await Role.countDocuments();

    // Get recent activities (last 5)
    const recentActivities = await Activity.find()
      .populate('user', 'fullName email')
      .sort({ createdAt: -1 })
      .limit(5);

    res.status(200).json({
      status: 'success',
      data: {
        totalUsers,
        activeUsers,
        newUsersToday,
        totalRoles,
        recentActivities
      }
    });
  } catch (error) {
    console.error('Error fetching admin dashboard stats:', error);
    res.status(500).json({
      status: 'failed',
      message: 'Failed to fetch dashboard statistics'
    });
  }
};

// Get activity logs with filtering and pagination
exports.getActivityLogs = async (req, res) => {
  try {
    console.log('🔍 Admin logs endpoint called with query:', req.query);

    // First, let's check if there are any activities at all
    const totalActivities = await Activity.countDocuments();
    console.log('📊 Total activities in database:', totalActivities);

    // If no activities exist, create some test activities
    if (totalActivities === 0) {
      console.log('🧪 Creating test activity logs...');
      const testActivities = [
        {
          user: req.user.id,
          action: 'user_login',
          description: 'Admin accessed activity logs page',
          entityType: 'system',
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        },
        {
          user: req.user.id,
          action: 'user_created',
          description: 'Created new user account',
          entityType: 'user',
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        },
        {
          user: req.user.id,
          action: 'project_created',
          description: 'Created new project',
          entityType: 'project',
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      ];

      await Activity.insertMany(testActivities);
      console.log('✅ Test activities created');
    }

    const {
      page = 1,
      limit = 20,
      level,
      userId,
      days = 30,
      action,
      entityType
    } = req.query;

    // Build filter query
    const filter = {};

    // Filter by date range
    if (days) {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - parseInt(days));
      filter.createdAt = { $gte: daysAgo };
    }

    // Filter by user
    if (userId && userId !== 'all') {
      filter.user = userId;
    }

    // Filter by action
    if (action && action !== 'all') {
      filter.action = action;
    }

    // Filter by entity type
    if (entityType && entityType !== 'all') {
      filter.entityType = entityType;
    }

    console.log('📊 Filter query:', filter);

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get logs with user information
    const logs = await Activity.find(filter)
      .populate('user', 'fullName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    console.log('📝 Found logs:', logs.length);

    // Get total count for pagination
    const totalLogs = await Activity.countDocuments(filter);
    const totalPages = Math.ceil(totalLogs / parseInt(limit));

    console.log('📊 Total logs:', totalLogs, 'Total pages:', totalPages);

    // Transform logs for frontend
    const transformedLogs = logs.map(log => ({
      _id: log._id,
      userId: log.user?._id,
      userEmail: log.user?.email,
      userName: log.user?.fullName,
      action: log.action,
      description: log.description,
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      timestamp: log.createdAt,
      level: getLogLevel(log.action),
      metadata: log.metadata
    }));

    res.status(200).json({
      status: 'success',
      data: {
        logs: transformedLogs,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalLogs,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching activity logs:', error);
    res.status(500).json({
      status: 'failed',
      message: 'Failed to fetch activity logs'
    });
  }
};

// Export activity logs as CSV
exports.exportActivityLogs = async (req, res) => {
  try {
    const {
      level,
      userId,
      days = 30,
      action,
      entityType
    } = req.query;

    // Build filter query (same as getActivityLogs)
    const filter = {};

    if (days) {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - parseInt(days));
      filter.createdAt = { $gte: daysAgo };
    }

    if (userId && userId !== 'all') {
      filter.user = userId;
    }

    if (action && action !== 'all') {
      filter.action = action;
    }

    if (entityType && entityType !== 'all') {
      filter.entityType = entityType;
    }

    // Get all logs matching filter
    const logs = await Activity.find(filter)
      .populate('user', 'fullName email')
      .sort({ createdAt: -1 });

    // Generate CSV content
    const csvHeader = 'Timestamp,User Name,User Email,Action,Description,IP Address,Entity Type,Entity Name\n';
    const csvRows = logs.map(log => {
      const timestamp = log.createdAt.toISOString();
      const userName = log.user?.fullName || 'Unknown';
      const userEmail = log.user?.email || 'Unknown';
      const action = log.action.replace(/_/g, ' ').toUpperCase();
      const description = `"${log.description.replace(/"/g, '""')}"`;
      const ipAddress = log.ipAddress || 'Unknown';
      const entityType = log.entityType || '';
      const entityName = log.entityName || '';
      
      return `${timestamp},${userName},${userEmail},${action},${description},${ipAddress},${entityType},${entityName}`;
    }).join('\n');

    const csvContent = csvHeader + csvRows;

    // Set response headers for CSV download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="activity-logs-${new Date().toISOString().split('T')[0]}.csv"`);
    
    res.status(200).send(csvContent);
  } catch (error) {
    console.error('Error exporting activity logs:', error);
    res.status(500).json({
      status: 'failed',
      message: 'Failed to export activity logs'
    });
  }
};

// Get activity statistics
exports.getActivityStats = async (req, res) => {
  try {
    const { days = 30 } = req.query;

    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - parseInt(days));

    // Get activity counts by action type
    const actionStats = await Activity.aggregate([
      { $match: { createdAt: { $gte: daysAgo } } },
      { $group: { _id: '$action', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get activity counts by user
    const userStats = await Activity.aggregate([
      { $match: { createdAt: { $gte: daysAgo } } },
      { $group: { _id: '$user', count: { $sum: 1 } } },
      { $lookup: { from: 'users', localField: '_id', foreignField: '_id', as: 'user' } },
      { $unwind: '$user' },
      { $project: { userName: '$user.fullName', userEmail: '$user.email', count: 1 } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);

    // Get daily activity counts
    const dailyStats = await Activity.aggregate([
      { $match: { createdAt: { $gte: daysAgo } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.status(200).json({
      status: 'success',
      data: {
        actionStats,
        userStats,
        dailyStats
      }
    });
  } catch (error) {
    console.error('Error fetching activity statistics:', error);
    res.status(500).json({
      status: 'failed',
      message: 'Failed to fetch activity statistics'
    });
  }
};

// Helper function to determine log level based on action
function getLogLevel(action) {
  const errorActions = ['user_deleted', 'project_rejected', 'password_reset'];
  const warningActions = ['user_role_changed', 'status_changed', 'budget_updated'];
  const successActions = ['user_created', 'project_created', 'project_approved', 'activity_completed'];
  
  if (errorActions.includes(action)) return 'error';
  if (warningActions.includes(action)) return 'warning';
  if (successActions.includes(action)) return 'success';
  return 'info';
}

// Delete old activity logs (cleanup function)
exports.cleanupOldLogs = async (req, res) => {
  try {
    const { days = 90 } = req.body;
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));

    const result = await Activity.deleteMany({
      createdAt: { $lt: cutoffDate }
    });

    // Log the cleanup activity
    await ActivityLogger.logActivity({
      userId: req.user.id,
      action: 'system_cleanup',
      description: `Cleaned up ${result.deletedCount} old activity logs older than ${days} days`,
      entityType: 'system',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.status(200).json({
      status: 'success',
      message: `Successfully deleted ${result.deletedCount} old activity logs`,
      data: { deletedCount: result.deletedCount }
    });
  } catch (error) {
    console.error('Error cleaning up old logs:', error);
    res.status(500).json({
      status: 'failed',
      message: 'Failed to cleanup old logs'
    });
  }
};
