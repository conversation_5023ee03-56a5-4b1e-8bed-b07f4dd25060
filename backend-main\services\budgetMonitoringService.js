const Project = require("../models/project.model");
const ActivityReport = require("../models/activityReport.model");
const ProjectActivities = require("../models/projectActivities.model");
const NotificationService = require("./notificationService");

class BudgetMonitoringService {
  // Check budget utilization for all projects
  static async checkBudgetUtilization() {
    try {
      console.log('🔍 Checking budget utilization for all projects...');
      
      const projects = await Project.find({ status: { $in: ['approved', 'inprogress'] } });
      
      for (const project of projects) {
        await this.checkProjectBudget(project._id);
      }
    } catch (error) {
      console.error("Error checking budget utilization:", error);
    }
  }

  // Check budget for a specific project
  static async checkProjectBudget(projectId) {
    try {
      const project = await Project.findById(projectId);
      if (!project || !project.initialBudget) return;

      // Get all activities for this project
      const activities = await ProjectActivities.find({ project: projectId });
      const activityIds = activities.map(activity => activity._id);

      // Get approved reports for these activities
      const approvedReports = await ActivityReport.find({
        activity: { $in: activityIds },
        approved: true
      });

      // Calculate total spent
      const totalSpent = approvedReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
      const budgetUtilization = (totalSpent / project.initialBudget) * 100;

      console.log(`📊 Project "${project.title}": Budget ${project.initialBudget}, Spent ${totalSpent}, Utilization ${budgetUtilization.toFixed(2)}%`);

      // Check for overuse (>100%)
      if (budgetUtilization > 100) {
        const overuseAmount = totalSpent - project.initialBudget;
        const overusePercentage = budgetUtilization - 100;

        console.log(`⚠️ Budget overuse detected for project "${project.title}" (${budgetUtilization.toFixed(2)}%)`);
        await NotificationService.notifyBudgetOveruse(projectId, overuseAmount, overusePercentage.toFixed(2));
      }
      // Check for significant underuse (<50% and project is >75% complete by time)
      else if (budgetUtilization < 50) {
        const projectDuration = new Date(project.endDate) - new Date(project.startDate);
        const timeElapsed = new Date() - new Date(project.startDate);
        const timeProgress = (timeElapsed / projectDuration) * 100;

        if (timeProgress > 75) {
          const underuseAmount = project.initialBudget - totalSpent;
          const underusePercentage = 100 - budgetUtilization;

          console.log(`📉 Budget underuse detected for project "${project.title}"`);
          await NotificationService.notifyBudgetUnderuse(projectId, underuseAmount, underusePercentage.toFixed(2));
        }
      }

      return {
        projectId,
        projectName: project.title,
        budget: project.initialBudget,
        spent: totalSpent,
        utilization: budgetUtilization
      };
    } catch (error) {
      console.error(`Error checking budget for project ${projectId}:`, error);
    }
  }

  // Check for schedule slippage
  static async checkScheduleSlippage() {
    try {
      console.log('📅 Checking for schedule slippage...');
      
      const projects = await Project.find({ 
        status: 'inprogress',
        endDate: { $exists: true }
      });

      for (const project of projects) {
        const activities = await ProjectActivities.find({ 
          project: project._id,
          status: { $ne: 'completed' },
          dueDate: { $lt: new Date() }
        });

        if (activities.length > 0) {
          // Calculate average delay
          const now = new Date();
          const totalDelay = activities.reduce((sum, activity) => {
            const delay = Math.ceil((now - new Date(activity.dueDate)) / (1000 * 60 * 60 * 24));
            return sum + delay;
          }, 0);
          
          const averageDelay = Math.ceil(totalDelay / activities.length);
          
          if (averageDelay > 0) {
            console.log(`⏰ Schedule slippage detected for project "${project.name}": ${averageDelay} days`);
            await NotificationService.notifyScheduleSlippage(project._id, averageDelay);
          }
        }
      }
    } catch (error) {
      console.error("Error checking schedule slippage:", error);
    }
  }

  // Monitor project completion and trigger notifications
  static async monitorProjectProgress() {
    try {
      console.log('📈 Monitoring project progress...');
      
      const projects = await Project.find({ status: 'inprogress' });
      
      for (const project of projects) {
        const activities = await ProjectActivities.find({ project: project._id });
        const completedActivities = activities.filter(activity => activity.status === 'completed');
        
        const completionPercentage = activities.length > 0 ? (completedActivities.length / activities.length) * 100 : 0;
        
        // Check if project should be marked as completed
        if (completionPercentage === 100 && project.status !== 'completed') {
          await Project.findByIdAndUpdate(project._id, { status: 'completed' });
          
          // Notify about project completion
          await NotificationService.notifyProjectCompletion(project._id);
        }
      }
    } catch (error) {
      console.error("Error monitoring project progress:", error);
    }
  }

  // Run all monitoring checks
  static async runAllChecks() {
    console.log('🔄 Running all budget and schedule monitoring checks...');
    
    await this.checkBudgetUtilization();
    await this.checkScheduleSlippage();
    await this.monitorProjectProgress();
    
    console.log('✅ All monitoring checks completed');
  }
}

module.exports = BudgetMonitoringService;
