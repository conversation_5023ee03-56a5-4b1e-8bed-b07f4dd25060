const Project = require("../models/project.model");
const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");
const User = require("../models/user.model");
const Role = require("../models/roles.model");
const ReportingService = require("../services/reportingService");
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

// Get comprehensive project analytics
exports.getProjectAnalytics = async (req, res) => {
  try {
    const filters = req.query;
    const analytics = await ReportingService.generateProjectAnalytics(filters);

    res.status(200).json({
      status: "success",
      data: analytics
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to generate project analytics"
    });
  }
};

// Get comprehensive activity analytics
exports.getActivityAnalytics = async (req, res) => {
  try {
    const filters = req.query;
    const analytics = await ReportingService.generateActivityAnalytics(filters);

    res.status(200).json({
      status: "success",
      data: analytics
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to generate activity analytics"
    });
  }
};

// Get comprehensive budget analytics
exports.getBudgetAnalytics = async (req, res) => {
  try {
    const filters = req.query;
    const analytics = await ReportingService.generateBudgetAnalytics(filters);

    res.status(200).json({
      status: "success",
      data: analytics
    });
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to generate budget analytics"
    });
  }
};

// Get comprehensive reports data for senior manager (legacy endpoint)
exports.getReportsData = async (req, res) => {
  try {
    // Get all projects with populated data
    const projects = await Project.find()
      .populate('assignedTo', 'fullName')
      .populate('createdBy', 'fullName')
      .lean();

    // Calculate project progress and budget data
    const projectsWithAnalytics = await Promise.all(
      projects.map(async (project) => {
        // Get activities for this project
        const totalActivities = await ProjectActivities.countDocuments({
          project: project._id,
        });
        const completedActivities = await ProjectActivities.countDocuments({
          project: project._id,
          status: "completed",
        });

        // Calculate progress
        const progress = totalActivities > 0 
          ? Math.round((completedActivities / totalActivities) * 100)
          : 0;

        // Get project activities first
        const projectActivities = await ProjectActivities.find({
          project: project._id
        });

        // Get activity reports for these activities
        const activityIds = projectActivities.map(activity => activity._id);
        const reports = await ActivityReport.find({
          activity: { $in: activityIds },
          approved: true // Use approved field instead of status
        });

        const budgetUsed = reports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
        const projectBudget = project.initialBudget || project.budget || 0;
        const budgetUtilization = projectBudget > 0
          ? Math.round((budgetUsed / projectBudget) * 100)
          : 0;

        // Debug logging
        console.log(`Project: ${project.title}, Activities: ${projectActivities.length}, Reports: ${reports.length}, Budget Used: ${budgetUsed}, Budget: ${projectBudget}, Utilization: ${budgetUtilization}%`);

        // Update project's usedBudget and budgetUtilization fields
        await Project.findByIdAndUpdate(project._id, {
          usedBudget: budgetUsed,
          budgetUtilization: budgetUtilization,
          remainingBudget: projectBudget - budgetUsed
        });

        return {
          ...project,
          name: project.title, // Add name field for backward compatibility
          progress,
          totalActivities,
          completedActivities,
          budgetUsed,
          usedBudget: budgetUsed, // Add both field names for compatibility
          budgetUtilization,
          budgetRemaining: (project.initialBudget || project.budget || 0) - budgetUsed,
          budget: project.initialBudget || project.budget || 0, // Ensure budget field is available
        };
      })
    );

    // Calculate overall statistics
    const totalProjects = projects.length;
    const completedProjects = projects.filter(p => p.status === 'completed').length;
    const activeProjects = projects.filter(p => p.status === 'inprogress').length;
    const pendingProjects = projects.filter(p => !p.status || p.status === 'pending').length;

    // Calculate total budget statistics
    const totalBudget = projects.reduce((sum, p) => sum + (p.initialBudget || p.budget || 0), 0);
    const totalBudgetUsed = projectsWithAnalytics.reduce((sum, p) => sum + (p.budgetUsed || 0), 0);
    const totalBudgetRemaining = totalBudget - totalBudgetUsed;
    const overallBudgetUtilization = totalBudget > 0
      ? Math.round((totalBudgetUsed / totalBudget) * 100)
      : 0;

    // Prepare chart data
    const projectProgressChartData = {
      labels: projectsWithAnalytics.map(p => p.title || p.name || 'Unnamed Project'),
      datasets: [{
        label: 'Project Progress (%)',
        data: projectsWithAnalytics.map(p => p.progress || 0),
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      }]
    };

    const budgetUtilizationChartData = {
      labels: projectsWithAnalytics.map(p => p.title || p.name || 'Unnamed Project'),
      datasets: [{
        label: 'Budget Utilization (%)',
        data: projectsWithAnalytics.map(p => p.budgetUtilization || 0),
        backgroundColor: 'rgba(16, 185, 129, 0.6)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      }]
    };

    const overallBudgetPieData = {
      labels: ['Used', 'Remaining'],
      datasets: [{
        data: [totalBudgetUsed, totalBudgetRemaining],
        backgroundColor: [
          'rgba(239, 68, 68, 0.6)',
          'rgba(34, 197, 94, 0.6)',
        ],
        borderColor: [
          'rgba(239, 68, 68, 1)',
          'rgba(34, 197, 94, 1)',
        ],
        borderWidth: 1,
      }]
    };

    const projectStatusPieData = {
      labels: ['Active', 'Completed', 'Pending'],
      datasets: [{
        data: [activeProjects, completedProjects, pendingProjects],
        backgroundColor: [
          'rgba(59, 130, 246, 0.6)',
          'rgba(34, 197, 94, 0.6)',
          'rgba(251, 191, 36, 0.6)',
        ],
        borderColor: [
          'rgba(59, 130, 246, 1)',
          'rgba(34, 197, 94, 1)',
          'rgba(251, 191, 36, 1)',
        ],
        borderWidth: 1,
      }]
    };

    res.status(200).json({
      status: "success",
      data: {
        projects: projectsWithAnalytics,
        statistics: {
          totalProjects,
          completedProjects,
          activeProjects,
          pendingProjects,
          totalBudget,
          totalBudgetUsed,
          totalBudgetRemaining,
          overallBudgetUtilization,
        },
        charts: {
          projectProgress: projectProgressChartData,
          budgetUtilization: budgetUtilizationChartData,
          overallBudgetPie: overallBudgetPieData,
          projectStatusPie: projectStatusPieData,
        }
      }
    });

  } catch (error) {
    console.error("Reports data error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch reports data",
    });
  }
};

// Get budget analytics for dashboard
exports.getBudgetAnalytics = async (req, res) => {
  try {
    // Get all active projects (use 'inprogress' status as per project model)
    const activeProjects = await Project.find({ status: 'inprogress' });
    
    let totalBudget = 0;
    let totalUsed = 0;
    const projectBudgets = [];

    for (const project of activeProjects) {
      // Get project activities first
      const projectActivities = await ProjectActivities.find({
        project: project._id
      });

      // Get activity reports for these activities
      const activityIds = projectActivities.map(activity => activity._id);
      const reports = await ActivityReport.find({
        activity: { $in: activityIds },
        approved: true // Use approved field instead of status
      });

      const budgetUsed = reports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
      
      totalBudget += project.budget || 0;
      totalUsed += budgetUsed;

      projectBudgets.push({
        name: project.name,
        budget: project.budget || 0,
        used: budgetUsed,
        remaining: (project.budget || 0) - budgetUsed,
        utilization: project.budget > 0 ? Math.round((budgetUsed / project.budget) * 100) : 0,
      });
    }

    const totalRemaining = totalBudget - totalUsed;
    const overallUtilization = totalBudget > 0 ? Math.round((totalUsed / totalBudget) * 100) : 0;

    // Prepare chart data for dashboard
    const budgetChartData = {
      labels: ['Used', 'Remaining'],
      datasets: [{
        data: [totalUsed, totalRemaining],
        backgroundColor: [
          'rgba(239, 68, 68, 0.6)',
          'rgba(34, 197, 94, 0.6)',
        ],
        borderColor: [
          'rgba(239, 68, 68, 1)',
          'rgba(34, 197, 94, 1)',
        ],
        borderWidth: 1,
      }]
    };

    res.status(200).json({
      status: "success",
      data: {
        totalBudget,
        totalUsed,
        totalRemaining,
        overallUtilization,
        projectBudgets,
        chartData: budgetChartData,
      }
    });

  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch budget analytics",
    });
  }
};

// Export project analytics as PDF
exports.exportProjectAnalyticsPDF = async (req, res) => {
  try {
    const filters = req.query;
    const analytics = await ReportingService.generateProjectAnalytics(filters);

    // Create PDF document
    const doc = new PDFDocument();
    const filename = `project-analytics-${new Date().toISOString().split('T')[0]}.pdf`;

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Pipe PDF to response
    doc.pipe(res);

    // Add content to PDF
    doc.fontSize(20).text('Project Analytics Report', 50, 50);
    doc.fontSize(12).text(`Generated on: ${new Date().toLocaleDateString()}`, 50, 80);

    // Summary section
    doc.fontSize(16).text('Summary', 50, 120);
    doc.fontSize(12)
       .text(`Total Projects: ${analytics.analytics.totalProjects}`, 50, 150)
       .text(`Total Budget: $${analytics.analytics.totalBudget.toLocaleString()}`, 50, 170)
       .text(`Total Used: $${analytics.analytics.totalUsedBudget.toLocaleString()}`, 50, 190)
       .text(`Total Remaining: $${analytics.analytics.totalRemainingBudget.toLocaleString()}`, 50, 210);

    // Status breakdown
    doc.fontSize(16).text('Status Breakdown', 50, 250);
    let yPos = 280;
    Object.entries(analytics.analytics.statusBreakdown).forEach(([status, count]) => {
      doc.fontSize(12).text(`${status}: ${count}`, 50, yPos);
      yPos += 20;
    });

    doc.end();
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: "Failed to export PDF"
    });
  }
};
