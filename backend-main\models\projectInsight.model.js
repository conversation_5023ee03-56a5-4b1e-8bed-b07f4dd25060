const mongoose = require("mongoose");

const projectInsightSchema = new mongoose.Schema({
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Project",
    required: true,
  },
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  lessonsLearned: {
    type: String,
    required: true,
  },
  recommendations: {
    type: String,
    required: true,
  },
  submittedAt: {
    type: Date,
    default: Date.now,
  },
}, { timestamps: true });

// Index for better query performance
projectInsightSchema.index({ project: 1, submittedBy: 1 });

const ProjectInsight = mongoose.model("ProjectInsight", projectInsightSchema);

module.exports = ProjectInsight;
