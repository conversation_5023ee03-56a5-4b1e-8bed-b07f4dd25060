const mongoose = require("mongoose");
const District = require("../models/districts.model");

const districts = [
  "Balaka",
  "Blantyre",
  "Chikwawa",
  "Chiradzulu",
  "Chitipa",
  "Dedza",
  "Dowa",
  "Karonga",
  "Kasungu",
  "Likoma",
  "Lilongwe",
  "Machinga",
  "Mangochi",
  "Mchinji",
  "Mulanje",
  "Mwanza",
  "Mzimba",
  "Neno",
  "Nkhata Bay",
  "Nkhotakota",
  "Nsanje",
  "Ntcheu",
  "Ntchisi",
  "Phalombe",
  "Rumphi",
  "Salima",
  "Thyolo",
  "Zomba",
];

async function addDistricts() {
  for (const name of districts) {
    try {
      const exists = await District.findOne({ name });
      if (!exists) {
        await District.create({ name });
        console.log(`Added: ${name}`);
      } else {
        console.log(`Already exists: ${name}`);
      }
    } catch (err) {
      console.error(`Error adding ${name}:`, err.message);
    }
  }
}

// Only disconnect if this file is run directly
if (require.main === module) {
  mongoose
    .connect("mongodb://127.0.0.1:27017/sprodeta")
    .then(async () => {
      await addDistricts();
      mongoose.disconnect();
    })
    .catch((err) => console.error("MongoDB Connection Error:", err));
}

module.exports = { addDistricts };
