const express = require('express');
const router = express.Router();
const ProjectPlan = require('../models/projectPlan.model.js');
const { protect, authorize } = require("../middleware/auth");
const {
  createProjectGoal,
  createObjective,
  getProjectPlanByProjectId,
} = require("../controllers/projectPlan.controller.js");

console.log("🚨 PROJECT PLAN ROUTES FILE LOADED 🚨");

router.post("/", async (req, res) => {
  console.log("🚨🚨🚨 PROJECT PLAN ROUTE HIT 🚨🚨🚨");
  console.log("Request body:", req.body);
  try {
    console.log("📝 Project plan creation request received");
    console.log("📋 Request body:", JSON.stringify(req.body, null, 2));

    const {
      project,
      objectives,
      goals,
      inputs,
      outputs,
      expectedOutcome,
      activities,
      deliverables,
      milestones,
      resources,
      risks,
      assumptions,
      status
    } = req.body;

    console.log("📊 Extracted fields:", {
      project,
      expectedOutcome,
      hasActivities: !!activities,
      hasDeliverables: !!deliverables,
      hasMilestones: !!milestones,
      hasResources: !!resources,
      hasRisks: !!risks,
      hasAssumptions: !!assumptions
    });

    // Create the project plan with all supported fields
    const planData = {
      project,
      expectedOutcome,
    };

    // Add optional fields if they exist
    if (objectives) planData.objectives = objectives;
    if (goals) planData.goals = goals;
    if (inputs) planData.inputs = inputs;
    if (outputs) planData.outputs = outputs;
    if (activities) planData.activities = activities;
    if (deliverables) planData.deliverables = deliverables;
    if (milestones) planData.milestones = milestones;
    if (resources) planData.resources = resources;
    if (risks) planData.risks = risks;
    if (assumptions) planData.assumptions = assumptions;
    if (status) planData.status = status;

    console.log("🚀 Creating project plan with data:", JSON.stringify(planData, null, 2));

    const newPlan = await ProjectPlan.create(planData);

    console.log("✅ Project plan created successfully:", newPlan._id);
    res.status(201).json(newPlan);
  } catch (err) {
    console.error("❌ Error creating project plan:", err);
    console.error("❌ Error details:", err.message);
    if (err.name === 'ValidationError') {
      const errors = Object.values(err.errors).map(e => e.message);
      console.error("❌ Validation errors:", errors);
      res.status(400).json({ error: "Validation failed", errors });
    } else {
      res.status(500).json({ error: "Server error while creating plan." });
    }
  }
});

router.post('/goal', protect, authorize('seniorManager'), createProjectGoal);
router.post('/objective', protect, authorize('seniorManager'), createObjective);
router.get('/plan/:projectId', protect, getProjectPlanByProjectId);

module.exports = router;
