const mongoose = require("mongoose");

const ProjectSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: [true, "Please provide the project title"],
      trim: true,
      minlength: [3, "Project title must be at least 3 characters long"],
      maxlength: [100, "Project title cannot exceed 100 characters"],
    },
    description: {
      type: String,
      required: [true, "Please provide the project description"],
      trim: true,
      minlength: [10, "Description must be at least 10 characters long"],
      maxlength: [1000, "Description cannot exceed 1000 characters"],
    },
    objectives: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProjectObjective",
    }],
    goals: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProjectGoal",
    }],
    activities: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProjectActivities",
    }],
    beneficiaries: {
      type: String,
      required: [true, "Please provide project beneficiaries"],
      trim: true,
      minlength: [10, "Beneficiaries description must be at least 10 characters long"],
    },
    location: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "District",
      required: [true, "Please provide the project location"],
    },
    startDate: {
      type: Date,
      required: [true, "Please provide the start date of the project"],
      validate: {
        validator: function(value) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return value >= today;
        },
        message: "Start date cannot be in the past"
      }
    },
    endDate: {
      type: Date,
      required: [true, "Please provide the end date of the project"],
      validate: {
        validator: function(value) {
          if (this.startDate) {
            return value > this.startDate;
          }
          return true;
        },
        message: "End date must be after start date"
      }
    },
    category: {
      type: String,
      required: [true, "Please provide the project category"],
      trim: true,
      minlength: [2, "Category must be at least 2 characters long"],
    },
    initialBudget: {
      type: Number,
      required: [true, "Please provide the initial project budget"],
      min: [1, "Budget must be greater than 0"],
      max: [1000000000, "Budget cannot exceed 1,000,000,000 MWK"],
    },
    usedBudget: {
      type: Number,
      default: 0,
      min: [0, "Used budget cannot be negative"],
    },
    remainingBudget: {
      type: Number,
      default: function() {
        return this.initialBudget || 0;
      },
    },
    budgetUtilization: {
      type: Number,
      default: 0,
      min: [0, "Budget utilization cannot be negative"],
      max: [100, "Budget utilization cannot exceed 100%"],
    },
    progressPercentage: {
      type: Number,
      default: 0,
      min: [0, "Progress cannot be negative"],
      max: [100, "Progress cannot exceed 100%"],
    },
    attachment: {
      type: String, // URL or file path for project approval documents
      required: [true, "Project approval documents are required"],
    },
    status: {
      type: String,
      enum: ["inprogress", "completed"],
      default: "inprogress",
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide the user that is creating this project"],
    },
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      default: null,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual field for backward compatibility
ProjectSchema.virtual('name').get(function() {
  return this.title;
});

module.exports = mongoose.model("Project", ProjectSchema);
