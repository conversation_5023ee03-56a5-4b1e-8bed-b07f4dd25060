const Project = require("../models/project.model");
const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");
const User = require("../models/user.model");
const ActivityLogger = require("../utils/activityLogger");

// Get senior manager dashboard statistics
exports.getSeniorManagerDashboardStats = async (req, res) => {
  try {
    console.log("🎯 Starting senior manager dashboard stats fetch...");

    // Get all projects (senior manager can see all projects)
    console.log("📊 Fetching all projects...");
    const allProjects = await Project.find({}).lean();
    console.log("📊 Found", allProjects.length, "projects");
    
    // Calculate total projects
    const totalProjects = allProjects.length;
    
    // Calculate active projects (in progress projects)
    const activeProjects = allProjects.filter(p => p.status === 'inprogress');
    const ongoingProjects = activeProjects.length;
    
    // Calculate completed projects (projects with status 'completed' OR where all activities are completed)
    console.log("📊 Calculating completed projects from", allProjects.length, "total projects...");

    // First, count projects that are explicitly marked as completed
    const explicitlyCompletedProjects = allProjects.filter(p => p.status === 'completed');
    console.log("📊 Found", explicitlyCompletedProjects.length, "explicitly completed projects");

    // Then, check in-progress projects where all activities might be completed
    const projectCompletionChecks = await Promise.all(
      activeProjects.map(async (project) => {
        try {
          const projectActivities = await ProjectActivities.find({ project: project._id });
          const completedActivities = await ProjectActivities.find({
            project: project._id,
            status: 'completed'
          });

          // Project is functionally completed if it has activities and all are completed
          const isFunctionallyCompleted = projectActivities.length > 0 && projectActivities.length === completedActivities.length;
          console.log(`📊 Project ${project.title || project.name}: ${projectActivities.length} total, ${completedActivities.length} completed, functionally complete: ${isFunctionallyCompleted}`);
          return isFunctionallyCompleted;
        } catch (error) {
          console.error("❌ Error processing project", project._id, ":", error.message);
          return false;
        }
      })
    );

    const functionallyCompletedCount = projectCompletionChecks.filter(Boolean).length;
    const completedProjectsCount = explicitlyCompletedProjects.length + functionallyCompletedCount;
    console.log("📊 Total completed projects:", completedProjectsCount, "(", explicitlyCompletedProjects.length, "explicit +", functionallyCompletedCount, "functional )");
    
    // Get all project managers (users assigned to projects)
    const projectManagerIds = [...new Set(allProjects.map(p => p.assignedTo).filter(Boolean))];
    const teamMembers = projectManagerIds.length;
    
    // Get recent projects (last 5) with complete information
    const recentProjectsWithDetails = await Promise.all(
      allProjects
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5)
        .map(async (project) => {
          try {
            // Calculate progress for this project
            const projectActivities = await ProjectActivities.find({ project: project._id });
            const completedActivities = await ProjectActivities.find({
              project: project._id,
              status: 'completed'
            });
            const progress = projectActivities.length > 0 ? Math.round((completedActivities.length / projectActivities.length) * 100) : 0;

            // Get assigned project manager
            const assignedManager = project.assignedTo ? await User.findById(project.assignedTo).select('fullName email') : null;

            return {
              id: project._id,
              _id: project._id,
              title: project.title,
              name: project.title || project.name,
              status: project.status,
              progress: progress,
              createdAt: project.createdAt,
              startDate: project.startDate,
              endDate: project.endDate,
              budget: project.initialBudget,
              location: project.location,
              assignedTo: assignedManager
            };
          } catch (error) {
            console.error("❌ Error processing recent project", project._id, ":", error.message);
            return {
              id: project._id,
              _id: project._id,
              title: project.title,
              name: project.title || project.name,
              status: project.status,
              progress: 0,
              createdAt: project.createdAt,
              startDate: project.startDate,
              endDate: project.endDate,
              budget: project.initialBudget,
              location: project.location,
              assignedTo: null
            };
          }
        })
    );

    const recentProjects = recentProjectsWithDetails;
    
    // Get project progress data for charts (all projects)
    console.log("📊 Calculating project progress for", Math.min(allProjects.length, 10), "projects...");
    const projectProgress = await Promise.all(
      allProjects.slice(0, 10).map(async (project) => {
        try {
          const totalActivities = await ProjectActivities.countDocuments({ project: project._id });
          const completedActivities = await ProjectActivities.countDocuments({
            project: project._id,
            status: 'completed'
          });

          const progress = totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;

          console.log(`📊 Project "${project.title || project.name}": ${completedActivities}/${totalActivities} activities completed (${progress}%)`);

          return {
            name: project.title || project.name,
            title: project.title || project.name, // Add title field for consistency
            progress,
            totalActivities,
            completedActivities,
            status: project.status // Include project status
          };
        } catch (error) {
          console.error("❌ Error calculating progress for project", project._id, ":", error.message);
          return {
            name: project.title || project.name || 'Unknown Project',
            title: project.title || project.name || 'Unknown Project',
            progress: 0,
            totalActivities: 0,
            completedActivities: 0,
            status: project.status
          };
        }
      })
    );

    console.log("📊 Project progress data calculated:", projectProgress.length, "projects");
    console.log("📊 Project progress summary:", projectProgress.map(p => `${p.name}: ${p.progress}%`));

    // Get recent activities across all projects
    console.log("📊 Fetching recent activities...");
    const recentActivities = await ActivityReport.find({})
      .populate({
        path: 'activity',
        select: 'title location project',
        populate: {
          path: 'project',
          select: 'title'
        }
      })
      .populate('submittedBy', 'fullName')
      .sort({ createdAt: -1 })
      .limit(5)
      .lean();
    console.log("📊 Found", recentActivities.length, "recent activities");

    console.log("✅ Senior manager dashboard stats calculated successfully");
    console.log("📊 Stats summary:", {
      totalProjects,
      ongoingProjects,
      completedProjects: completedProjectsCount,
      teamMembers,
      recentProjectsCount: recentProjects.length,
      projectProgressCount: projectProgress.length,
      recentActivitiesCount: recentActivities.length
    });

    res.status(200).json({
      status: "success",
      data: {
        totalProjects,
        ongoingProjects,
        completedProjects: completedProjectsCount,
        teamMembers,
        recentProjects,
        projectProgress,
        recentActivities
      }
    });

  } catch (error) {
    console.error("❌ Senior manager dashboard stats error:", error);
    console.error("❌ Error stack:", error.stack);
    console.error("❌ Error name:", error.name);
    console.error("❌ Error message:", error.message);

    res.status(500).json({
      status: "failed",
      message: "Failed to fetch dashboard statistics",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get budget analytics for senior manager
exports.getBudgetAnalytics = async (req, res) => {
  try {
    // Get all projects with their budget data
    const allProjects = await Project.find({}).lean();
    
    // Calculate total budget across all projects
    const totalBudget = allProjects.reduce((sum, project) => sum + (project.initialBudget || 0), 0);
    
    // Calculate total approved budget (only in-progress projects)
    const approvedProjects = allProjects.filter(p => p.status === 'inprogress');
    const totalApproved = approvedProjects.reduce((sum, project) => sum + (project.initialBudget || 0), 0);
    
    // Calculate total spent from approved activity reports
    const approvedReports = await ActivityReport.find({ approved: true });
    const totalSpent = approvedReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
    
    // Calculate remaining budget
    const totalRemaining = totalApproved - totalSpent;
    
    // Calculate budget utilization percentage
    const budgetUtilization = totalApproved > 0 ? Math.round((totalSpent / totalApproved) * 100) : 0;
    
    // Get budget distribution by project
    const budgetDistribution = await Promise.all(
      approvedProjects.map(async (project) => {
        // Get activities for this project first, then get their reports
        const projectActivities = await ProjectActivities.find({ project: project._id });
        const activityIds = projectActivities.map(activity => activity._id);
        const projectReports = await ActivityReport.find({
          activity: { $in: activityIds },
          approved: true
        });
        
        const projectSpent = projectReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
        const projectRemaining = (project.initialBudget || 0) - projectSpent;
        const projectUtilization = project.initialBudget > 0 ? Math.round((projectSpent / project.initialBudget) * 100) : 0;

        return {
          name: project.title || project.name,
          budget: project.initialBudget || 0,
          spent: projectSpent,
          remaining: projectRemaining,
          utilization: projectUtilization
        };
      })
    );
    
    res.status(200).json({
      status: "success",
      data: {
        totalBudget,
        totalApproved,
        totalSpent,
        totalRemaining,
        budgetUtilization,
        budgetDistribution
      }
    });

  } catch (error) {
    console.error("Budget analytics error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch budget analytics"
    });
  }
};

// Get all projects for senior manager
exports.getAllProjects = async (req, res) => {
  try {
    const projects = await Project.find({})
      .populate('createdBy', 'fullName email')
      .populate('assignedTo', 'fullName email')
      .sort({ createdAt: -1 })
      .lean();

    // Add progress information to each project
    const projectsWithProgress = await Promise.all(
      projects.map(async (project) => {
        const totalActivities = await ProjectActivities.countDocuments({ project: project._id });
        const completedActivities = await ProjectActivities.countDocuments({ 
          project: project._id, 
          status: 'completed' 
        });
        
        const progress = totalActivities > 0 ? Math.round((completedActivities / totalActivities) * 100) : 0;
        
        // Get total spent on this project
        const projectActivities = await ProjectActivities.find({ project: project._id });
        const activityIds = projectActivities.map(activity => activity._id);
        const projectReports = await ActivityReport.find({
          activity: { $in: activityIds },
          approved: true
        });
        const totalSpent = projectReports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
        
        return {
          ...project,
          progress,
          totalActivities,
          completedActivities,
          totalSpent,
          remaining: (project.initialBudget || 0) - totalSpent
        };
      })
    );

    res.status(200).json({
      status: "success",
      data: projectsWithProgress
    });

  } catch (error) {
    console.error("Get all projects error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch projects"
    });
  }
};

// Get comprehensive reports for senior manager
exports.getComprehensiveReports = async (req, res) => {
  try {
    // Get all activity reports with detailed information
    const reports = await ActivityReport.find({})
      .populate({
        path: 'activity',
        select: 'title budget location project',
        populate: {
          path: 'project',
          select: 'title initialBudget'
        }
      })
      .populate('submittedBy', 'fullName email')
      .populate('approvedBy', 'fullName')
      .sort({ createdAt: -1 })
      .lean();

    // Calculate summary statistics
    const totalReports = reports.length;
    const approvedReports = reports.filter(r => r.approved === true).length;
    const pendingReports = reports.filter(r => r.approved === false && !r.rejectedBy).length;
    const rejectedReports = reports.filter(r => r.rejectedBy).length;

    const totalAmountReported = reports.reduce((sum, report) => sum + (report.amountSpent || 0), 0);
    const totalAmountApproved = reports
      .filter(r => r.approved === true)
      .reduce((sum, report) => sum + (report.amountSpent || 0), 0);

    res.status(200).json({
      status: "success",
      data: {
        reports,
        summary: {
          totalReports,
          approvedReports,
          pendingReports,
          rejectedReports,
          totalAmountReported,
          totalAmountApproved
        }
      }
    });

  } catch (error) {
    console.error("Get comprehensive reports error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch comprehensive reports"
    });
  }
};


