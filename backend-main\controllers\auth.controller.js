const User = require("../models/user.model");
const jwt = require("jsonwebtoken");
const ActivityLogger = require("../utils/activityLogger");

// Generate JWT Token
const generateToken = (id, role) => {
  console.log("🔐 Generating token for user:", { id, role });
  const token = jwt.sign({ id, role }, process.env.JWT_SECRET, {
    expiresIn: "30d",
  });
  console.log("🔐 Token generated successfully, length:", token.length);
  return token;
};

// Set token cookie
const sendTokenResponse = (user, statusCode, res) => {
  const token = generateToken(user._id, user.role);

  const options = {
    expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict"
  };

  res.status(statusCode)
    .cookie("token", token, options)
    .json({
      status: "success",
      token,
      user: {
        id: user._id,
        fullName: user.fullName,
        email: user.email,
        role: user.role,
        district: user.district,
        department: user.department
      }
    });
};

exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    console.log("🔐 Login attempt for:", email);
    console.log("🔐 JWT_SECRET available:", !!process.env.JWT_SECRET);

    // Validate email and password
    if (!email || !password) {
      return res.status(400).json({
        status: "failed",
        message: "Please provide email and password"
      });
    }

    // Check if user exists and password is correct
    console.log("🔐 Looking for user with email:", email);
    const user = await User.findOne({ email }).select("+password").populate("role", "name");

    if (!user) {
      console.error("🔐 User not found with email:", email);
      return res.status(401).json({
        status: "failed",
        message: "Invalid credentials"
      });
    }

    console.log("🔐 User found:", {
      id: user._id,
      email: user.email,
      fullName: user.fullName,
      role: user.role?.name || 'No role',
      roleId: user.role?._id
    });

    // Special check for senior manager
    if (user.role?.name === 'seniorManager') {
      console.log("👔 Senior manager login detected!");
    }

    const isPasswordCorrect = await user.matchPassword(password);

    if (!isPasswordCorrect) {
      console.error("🔐 Password incorrect for user:", email);
      return res.status(401).json({
        status: "failed",
        message: "Invalid credentials"
      });
    }

    console.log("🔐 Password verified successfully for user:", email);

    // Update user online status and login tracking
    const token = generateToken(user._id, user.role);
    await User.findByIdAndUpdate(user._id, {
      isOnline: true,
      lastLogin: new Date(),
      lastSeen: new Date(),
      sessionToken: token
    });

    // Log the login activity
    await ActivityLogger.userLogin(user._id, req);

    console.log("🔐 Sending token response for user:", user.email);
    // Send token response
    sendTokenResponse(user, 200, res);

  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

exports.logout = async (req, res) => {
  try {
    // Update user offline status if user is authenticated
    if (req.user && req.user.id) {
      await User.findByIdAndUpdate(req.user.id, {
        isOnline: false,
        lastSeen: new Date(),
        sessionToken: null
      });

      // Log the logout activity
      await ActivityLogger.userLogout(req.user.id, req);
    }

    res.cookie("token", "none", {
      expires: new Date(Date.now() + 10 * 1000), // 10 seconds
      httpOnly: true,
    });

    res.status(200).json({
      status: "success",
      message: "User logged out successfully"
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

// Test endpoint to verify backend is working
exports.testAuth = async (req, res) => {
  try {
    res.status(200).json({
      status: "success",
      message: "Auth controller is working",
      user: req.user ? {
        id: req.user._id,
        email: req.user.email,
        role: req.user.role?.name
      } : null,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Test endpoint error"
    });
  }
};

// Forgot password - send reset email
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        status: "failed",
        message: "Please provide email address"
      });
    }

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "No user found with this email address"
      });
    }

    // Generate reset token
    const resetToken = user.createResetToken();
    await user.save({ validateBeforeSave: false });

    // Create reset URL
    const resetUrl = `${process.env.FRONTEND_URL || 'http://*************:3000'}/reset-password?token=${resetToken}`;

    try {
      // Send email with reset link
      const { sendPasswordResetMail } = require("../services/email.service");
      await sendPasswordResetMail(user.email, user.fullName, resetUrl, resetToken);

      console.log(`📧 Password reset email sent to: ${user.email}`);

      res.status(200).json({
        status: "success",
        message: "Password reset email sent successfully"
      });
    } catch (emailError) {
      console.error("Email sending failed:", emailError);

      // Clear reset token if email fails
      user.resetPasswordToken = undefined;
      user.resetPasswordExpire = undefined;
      await user.save({ validateBeforeSave: false });

      return res.status(500).json({
        status: "failed",
        message: "Failed to send reset email. Please try again."
      });
    }
  } catch (error) {
    console.error("Forgot password error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong. Please try again."
    });
  }
};

// Reset password with token
exports.resetPassword = async (req, res) => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({
        status: "failed",
        message: "Please provide reset token and new password"
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        status: "failed",
        message: "Password must be at least 6 characters long"
      });
    }

    // Hash the token and find user
    const crypto = require("crypto");
    const hashedToken = crypto.createHash("sha256").update(token).digest("hex");

    const user = await User.findOne({
      resetPasswordToken: hashedToken,
      resetPasswordExpire: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        status: "failed",
        message: "Invalid or expired reset token"
      });
    }

    // Set new password
    user.password = newPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save();

    console.log(`🔐 Password reset successful for user: ${user.email}`);

    // Log the password reset activity
    await ActivityLogger.passwordReset(
      user._id,
      user._id,
      user.fullName,
      req
    );

    res.status(200).json({
      status: "success",
      message: "Password reset successful. You can now login with your new password."
    });
  } catch (error) {
    console.error("Reset password error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong. Please try again."
    });
  }
};

exports.getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .select("-password")
      .populate("role", "name")
      .populate("district", "name")
      .populate("department", "name");

    if (!user) {
      return res.status(404).json({
        status: "failed",
        message: "User not found"
      });
    }

    res.status(200).json({
      status: "success",
      data: user
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

// Test authentication endpoint
exports.testAuth = async (req, res) => {
  try {
    console.log('🧪 Auth test endpoint called');
    console.log('👤 User from token:', {
      id: req.user.id,
      fullName: req.user.fullName,
      email: req.user.email,
      role: req.user.role?.name
    });

    res.status(200).json({
      status: "success",
      message: "Authentication working correctly",
      user: {
        id: req.user.id,
        fullName: req.user.fullName,
        email: req.user.email,
        role: req.user.role?.name
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Auth test error:", error);
    res.status(500).json({
      status: "failed",
      message: "Auth test failed",
      error: error.message
    });
  }
};