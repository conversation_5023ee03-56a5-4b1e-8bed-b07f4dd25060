const mongoose = require("mongoose");

const ProjectActivitiesSchema = new mongoose.Schema(
  {
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Project",
      required: [true, "Please provide the project reference"],
    },
    title: {
      type: String,
      required: [true, "Please provide the title of the project activity"],
      trim: true,
      minlength: [3, "Activity title must be at least 3 characters long"],
      maxlength: [200, "Activity title cannot exceed 200 characters"],
    },
    description: {
      type: String,
      required: [true, "Please provide the description of the project activity"],
      trim: true,
      minlength: [10, "Activity description must be at least 10 characters long"],
      maxlength: [1000, "Activity description cannot exceed 1000 characters"],
    },
    budget: {
      type: Number,
      default: 0, // Will be set by project manager during assignment
    },
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      default: null, // Will be assigned by project manager later
    },
    assignedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User", // Project manager who assigned the activity
      default: null,
    },
    assignmentDate: {
      type: Date,
      default: null, // When the activity was assigned to field officer
    },
    startDate: {
      type: Date,
      default: null, // Will be set by project manager during assignment
    },
    endDate: {
      type: Date,
      default: null, // Will be set by project manager during assignment
    },
    duration: {
      type: Number, // in days
      default: 0,
    },
    location: {
      type: String,
      default: "",
    },
    targetOutcome: {
      type: String,
      default: "", // Will be detailed by project manager during assignment
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide the user who created this activity"],
    },
    status: {
      type: String,
      enum: ["pending", "inprogress", "completed"],
      default: "pending",
    },
    priority: {
      type: String,
      enum: ["high", "medium", "low"],
      default: "medium",
    },
    kpis: {
      type: [{
        name: {
          type: String,
          trim: true,
          maxlength: [200, "KPI name cannot exceed 200 characters"],
        },
        target: {
          type: String,
          trim: true,
          maxlength: [500, "KPI target cannot exceed 500 characters"],
        },
        description: {
          type: String,
          trim: true,
          maxlength: [1000, "KPI description cannot exceed 1000 characters"],
          default: ""
        }
      }],
      default: []
    },
  },
  { timestamps: true }
);

// 🔁 Calculate duration before saving
ProjectActivitiesSchema.pre("save", function (next) {
  if (this.startDate && this.endDate) {
    const diffTime = Math.abs(this.endDate - this.startDate);
    this.duration = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // duration in days
  }
  next();
});

// Indexes for better query performance
ProjectActivitiesSchema.index({ project: 1 });
ProjectActivitiesSchema.index({ status: 1 });
ProjectActivitiesSchema.index({ assignedTo: 1 });
ProjectActivitiesSchema.index({ assignedBy: 1 });
ProjectActivitiesSchema.index({ createdBy: 1 });
ProjectActivitiesSchema.index({ startDate: 1 });
ProjectActivitiesSchema.index({ endDate: 1 });
ProjectActivitiesSchema.index({ priority: 1 });

module.exports = mongoose.model("ProjectActivities", ProjectActivitiesSchema, "projectactivities");
