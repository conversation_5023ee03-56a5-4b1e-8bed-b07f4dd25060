const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");
const User = require("../models/user.model");
const Role = require("../models/roles.model");

exports.getAssignedActivities = async (req, res) => {
  try {
    console.log('🔄 Field Officer - Getting assigned activities for user:', req.user?.id);

    // Get user ID from authenticated user
    const userId = req.user.id;

    // Get activities assigned to the logged-in user with full project manager details
    const assignedActivities = await ProjectActivities.find({ assignedTo: userId })
      .populate({
        path: 'project',
        select: 'title name description initialBudget location',
        populate: {
          path: 'assignedTo',
          select: 'fullName email phoneNumber'
        }
      })
      .populate('assignedTo', 'fullName email')
      .populate('createdBy', 'fullName email');

    console.log('✅ Field Officer - Found assigned activities:', assignedActivities.length);

    res.status(200).json({
      status: "success",
      data: assignedActivities
    });
  } catch (error) {
    console.error('❌ Field Officer - Error getting assigned activities:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

exports.getActiveProjects = async (req, res) => {
  try {
    // Get user ID from authenticated user
    const userId = req.user.id;
    
    // Get activities that are assigned to the logged-in user and are not completed
    const activeActivities = await ProjectActivities.find({
      assignedTo: userId,
      status: { $ne: "completed" }
    })
      .populate('project', 'title name initialBudget')
      .populate('assignedTo', 'fullName email');

    res.status(200).json({ 
      status: "success", 
      data: activeActivities 
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

exports.getTeamForActivity = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // Get the activity to find the project
    const activity = await ProjectActivities.findById(id).populate('project');
    
    if (!activity) {
      return res.status(404).json({
        status: "failed",
        message: "Activity not found"
      });
    }

    // Verify the user is assigned to this activity
    if (activity.assignedTo.toString() !== userId) {
      return res.status(403).json({
        status: "failed",
        message: "Not authorized to view this activity's team"
      });
    }

    // Get all field officers assigned to activities in the same project
    const teamMembers = await ProjectActivities.find({
      project: activity.project._id
    })
      .populate('assignedTo', 'fullName email phoneNumber district')
      .populate('assignedTo.district', 'name');

    // Extract unique team members
    const uniqueTeamMembers = teamMembers
      .map(item => item.assignedTo)
      .filter((member, index, arr) => 
        arr.findIndex(m => m._id.toString() === member._id.toString()) === index
      );

    res.status(200).json({ 
      status: "success", 
      data: uniqueTeamMembers 
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

exports.getPendingReports = async (req, res) => {
  try {
    console.log('🔄 Field Officer - Getting pending reports for user:', req.user?.id);

    // Get user ID from authenticated user
    const userId = req.user.id;

    // Get reports submitted by the logged-in user that haven't been approved yet
    const pendingReports = await ActivityReport.find({
      submittedBy: userId,
      approved: { $ne: true }
    })
      .populate('activity', 'title dueDate location')
      .populate('submittedBy', 'fullName email');

    console.log('✅ Field Officer - Found pending reports:', pendingReports.length);

    res.status(200).json({
      status: "success",
      data: pendingReports
    });
  } catch (error) {
    console.error('❌ Field Officer - Error getting pending reports:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};

exports.getAllMyReports = async (req, res) => {
  try {
    console.log('🔄 Field Officer - Getting all reports for user:', req.user?.id);

    // Get user ID from authenticated user
    const userId = req.user.id;

    // Get ALL reports submitted by the logged-in user (approved and pending)
    const allReports = await ActivityReport.find({
      submittedBy: userId
    })
      .populate({
        path: 'activity',
        select: 'title dueDate location',
        populate: {
          path: 'project',
          select: 'name'
        }
      })
      .populate('submittedBy', 'fullName email')
      .populate('approvedBy', 'fullName')
      .populate('rejectedBy', 'fullName')
      .sort({ createdAt: -1 }); // Sort by newest first

    console.log('✅ Field Officer - Found all reports:', allReports.length);

    res.status(200).json({
      status: "success",
      data: allReports
    });
  } catch (error) {
    console.error('❌ Field Officer - Error getting all reports:', error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again.",
    });
  }
};