const mongoose = require("mongoose");
const User = require("./models/user.model");
const Role = require("./models/roles.model");
const District = require("./models/district.model");
const Department = require("./models/department.model");
require("dotenv").config();

async function fixSeniorManager() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.DATABASE_URL || "mongodb://127.0.0.1:27017/sprodeta");
    console.log("✅ Connected to MongoDB");

    // Check if senior manager role exists
    console.log("\n🔍 Checking senior manager role...");
    const seniorManagerRole = await Role.findOne({ name: "seniorManager" });
    if (!seniorManagerRole) {
      console.log("❌ Senior manager role not found. Creating it...");
      const newRole = await Role.create({ name: "seniorManager" });
      console.log("✅ Created senior manager role:", newRole._id);
    } else {
      console.log("✅ Senior manager role exists:", seniorManagerRole._id);
    }

    // Check existing users with senior manager role
    console.log("\n🔍 Checking existing senior manager users...");
    const existingSeniorManagers = await User.find({}).populate("role", "name");
    const seniorManagers = existingSeniorManagers.filter(user => user.role?.name === "seniorManager");
    
    console.log("📊 Found", seniorManagers.length, "senior manager(s):");
    seniorManagers.forEach(user => {
      console.log(`   - ${user.fullName} (${user.email})`);
    });

    // Check if we need to create a senior manager user
    if (seniorManagers.length === 0) {
      console.log("\n🔧 No senior manager found. Creating one...");
      
      // Get required data
      const role = await Role.findOne({ name: "seniorManager" });
      let district = await District.findOne({ name: "Lilongwe" });
      if (!district) {
        district = await District.findOne();
      }
      let department = await Department.findOne({ name: "Management" });
      if (!department) {
        department = await Department.findOne();
      }

      if (!district || !department) {
        console.log("❌ Missing required district or department. Please run the full seeder first.");
        return;
      }

      // Create senior manager user
      const seniorManagerUser = await User.create({
        fullName: "Senior Manager",
        email: "<EMAIL>",
        password: "Senior@123",
        phoneNumber: "+265777123456",
        role: role._id,
        district: district._id,
        department: department._id,
        bio: "Senior manager with oversight of all projects",
        experience: 10,
        skills: ["Strategic Planning", "Project Oversight", "Budget Management"]
      });

      console.log("✅ Created senior manager user:");
      console.log("📧 Email: <EMAIL>");
      console.log("🔑 Password: Senior@123");
    }

    // Verify the fix
    console.log("\n🔍 Final verification...");
    const finalCheck = await User.findOne({ email: "<EMAIL>" }).populate("role", "name");
    if (finalCheck) {
      console.log("✅ Senior manager user verified:");
      console.log(`   - Name: ${finalCheck.fullName}`);
      console.log(`   - Email: ${finalCheck.email}`);
      console.log(`   - Role: ${finalCheck.role?.name}`);
      console.log(`   - Role ID: ${finalCheck.role?._id}`);
    } else {
      console.log("❌ Senior manager user still not found");
    }

    // Also check the test user
    const testUser = await User.findOne({ email: "<EMAIL>" }).populate("role", "name");
    if (testUser) {
      console.log("✅ Test senior manager user found:");
      console.log(`   - Name: ${testUser.fullName}`);
      console.log(`   - Email: ${testUser.email}`);
      console.log(`   - Role: ${testUser.role?.name}`);
    }

    console.log("\n✅ Senior manager fix completed!");
    console.log("\n🔑 Try these credentials:");
    console.log("   - <EMAIL> / Senior@123");
    console.log("   - <EMAIL> / password123 (if exists)");

  } catch (error) {
    console.error("❌ Error fixing senior manager:", error);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
  }
}

// Run the fix
if (require.main === module) {
  fixSeniorManager();
}

module.exports = { fixSeniorManager };
