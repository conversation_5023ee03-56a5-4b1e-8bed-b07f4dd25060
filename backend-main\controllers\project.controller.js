const Project = require("../models/project.model");
const ProjectPlan = require("../models/projectPlan.model");
const ProjectObjective = require("../models/projectObjectives.model");
const ProjectGoal = require("../models/projectGoals.model");
const ProjectActivities = require("../models/projectActivities.model");
const User = require("../models/user.model");
const Role = require("../models/roles.model");
const NotificationService = require("../services/notificationService");
const {
  newProjectToAccountant,
  sendBudgetApprovalMail,
  sendProjectAssignmentMail,
  sendActivityAssignmentMail,
  sendDetailedActivityAssignmentMail,
} = require("../services/email.service");

exports.createProject = async (req, res) => {
  try {
    const {
      title,
      description,
      objectives,
      goals,
      beneficiaries,
      location,
      startDate,
      endDate,
      category,
      initialBudget,
      attachment,
      createdBy
    } = req.body;

    // Comprehensive validation
    const errors = [];

    // Title validation
    if (!title || !title.trim()) {
      errors.push("Project title is required");
    } else if (title.trim().length < 3) {
      errors.push("Project title must be at least 3 characters long");
    } else if (title.trim().length > 100) {
      errors.push("Project title cannot exceed 100 characters");
    }

    // Description validation
    if (!description || !description.trim()) {
      errors.push("Project description is required");
    } else if (description.trim().length < 10) {
      errors.push("Description must be at least 10 characters long");
    }

    // Objectives validation
    if (!objectives || !Array.isArray(objectives) || objectives.length === 0) {
      errors.push("At least one objective is required");
    } else {
      const validObjectives = objectives.filter(obj => obj && obj.trim());
      if (validObjectives.length === 0) {
        errors.push("All objectives must be filled and cannot be empty");
      }
    }

    // Goals validation
    if (!goals || !Array.isArray(goals) || goals.length === 0) {
      errors.push("At least one goal is required");
    } else {
      const validGoals = goals.filter(goal => goal && goal.trim());
      if (validGoals.length === 0) {
        errors.push("All goals must be filled and cannot be empty");
      }
    }

    // Beneficiaries validation
    if (!beneficiaries || !beneficiaries.trim()) {
      errors.push("Project beneficiaries are required");
    } else if (beneficiaries.trim().length < 10) {
      errors.push("Beneficiaries description must be at least 10 characters long");
    }

    // Location validation
    if (!location || !location.trim()) {
      errors.push("Project location (district) is required");
    }

    // Date validation
    if (!startDate) {
      errors.push("Start date is required");
    }
    if (!endDate) {
      errors.push("End date is required");
    }
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (start < today) {
        errors.push("Start date cannot be in the past");
      }
      if (end <= start) {
        errors.push("End date must be after start date");
      }

      // Check project duration (max 5 years)
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      if (diffDays > 1825) {
        errors.push("Project duration cannot exceed 5 years");
      }
    }

    // Category validation
    if (!category || !category.trim()) {
      errors.push("Project category is required");
    }

    // Budget validation
    if (!initialBudget) {
      errors.push("Initial budget is required");
    } else {
      const budget = Number(initialBudget);
      if (isNaN(budget) || budget <= 0) {
        errors.push("Budget must be a valid positive number");
      } else if (budget > 1000000000) {
        errors.push("Budget cannot exceed 1,000,000,000 MWK");
      }
    }

    // Attachment validation
    if (!attachment || !attachment.trim()) {
      errors.push("Project approval documents are required");
    }

    // Return validation errors if any
    if (errors.length > 0) {
      return res.status(400).json({
        status: "failed",
        message: "Validation failed",
        errors: errors
      });
    }

    // First create the project without objectives and goals
    const newProject = await Project.create({
      title,
      description,
      objectives: [], // Will be populated after creating objective documents
      goals: [], // Will be populated after creating goal documents
      activities: [], // Will be populated during project planning
      beneficiaries,
      location,
      startDate,
      endDate,
      category,
      initialBudget,
      attachment,
      createdBy,
      status: "inprogress" // Changed from "draft" to match new enum
    });

    // Create objectives as separate documents
    const objectiveIds = [];
    if (objectives && Array.isArray(objectives)) {
      for (const objectiveText of objectives) {
        if (objectiveText && objectiveText.trim()) {
          const objective = await ProjectObjective.create({
            project: newProject._id,
            description: objectiveText.trim(),
            createdBy: createdBy,
            status: "pending"
          });
          objectiveIds.push(objective._id);
        }
      }
    }

    // Create goals as separate documents
    const goalIds = [];
    if (goals && Array.isArray(goals)) {
      for (const goalText of goals) {
        if (goalText && goalText.trim()) {
          const goal = await ProjectGoal.create({
            project: newProject._id,
            description: goalText.trim(),
            createdBy: createdBy,
            status: "pending"
          });
          goalIds.push(goal._id);
        }
      }
    }

    // Update the project with the objective and goal references
    newProject.objectives = objectiveIds;
    newProject.goals = goalIds;
    await newProject.save();

    // Populate the references for the response
    const populatedProject = await Project.findById(newProject._id)
      .populate('objectives')
      .populate('goals')
      .populate('location', 'name')
      .populate('createdBy', 'fullName email');

    // Notify accountants about project creation
    try {
      const NotificationService = require('../services/notificationService');
      await NotificationService.notifyProjectCreation(newProject._id, createdBy);
      console.log('✅ Project creation notification sent to accountants');
    } catch (notificationError) {
      console.error('❌ Failed to send project creation notification:', notificationError);
      // Don't fail the project creation if notification fails
    }

    res.status(201).json({
      status: "success",
      message: "Project created successfully! Please proceed to project planning.",
      data: {
        projectId: newProject._id,
        project: populatedProject
      }
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again",
    });
  }
};

exports.createProjectPlan = async (req, res) => {
  try {
    console.log("📝 Creating project plan with data:", {
      projectId: req.body.projectId,
      activitiesCount: req.body.activities?.length || 0,
      deliverablesCount: req.body.deliverables?.length || 0,
      milestonesCount: req.body.milestones?.length || 0,
      resourcesCount: req.body.resources?.length || 0,
      risksCount: req.body.risks?.length || 0,
      assumptionsCount: req.body.assumptions?.length || 0
    });

    const {
      projectId,
      activities,
      deliverables,
      milestones,
      resources,
      risks,
      assumptions
    } = req.body;

    // Comprehensive validation
    const errors = [];

    // Project ID validation
    if (!projectId) {
      errors.push("Project ID is required");
    }

    // Activities validation
    if (!activities || !Array.isArray(activities) || activities.length === 0) {
      errors.push("At least one activity is required");
    } else {
      activities.forEach((activity, index) => {
        if (!activity.title || !activity.title.trim()) {
          errors.push(`Activity ${index + 1}: Title is required`);
        }
        if (!activity.description || !activity.description.trim()) {
          errors.push(`Activity ${index + 1}: Description is required`);
        }
      });
    }

    // Deliverables validation (optional but validate if provided)
    if (deliverables && Array.isArray(deliverables) && deliverables.length > 0) {
      deliverables.forEach((deliverable, index) => {
        if (!deliverable.title || !deliverable.title.trim()) {
          errors.push(`Deliverable ${index + 1}: Title is required`);
        }
        if (!deliverable.description || !deliverable.description.trim()) {
          errors.push(`Deliverable ${index + 1}: Description is required`);
        }
        if (!deliverable.deadline) {
          errors.push(`Deliverable ${index + 1}: Deadline is required`);
        }
      });
    }

    // Milestones validation (optional but validate if provided)
    if (milestones && Array.isArray(milestones) && milestones.length > 0) {
      milestones.forEach((milestone, index) => {
        if (!milestone.title || !milestone.title.trim()) {
          errors.push(`Milestone ${index + 1}: Title is required`);
        }
        if (!milestone.description || !milestone.description.trim()) {
          errors.push(`Milestone ${index + 1}: Description is required`);
        }
        if (!milestone.deadline) {
          errors.push(`Milestone ${index + 1}: Deadline is required`);
        }
      });
    }

    // Resources validation (optional but validate if provided)
    if (resources && Array.isArray(resources) && resources.length > 0) {
      resources.forEach((resource, index) => {
        if (!resource.name || !resource.name.trim()) {
          errors.push(`Resource ${index + 1}: Name is required`);
        }
        if (!resource.quantity || resource.quantity <= 0) {
          errors.push(`Resource ${index + 1}: Quantity must be greater than 0`);
        }
        if (resource.cost < 0) {
          errors.push(`Resource ${index + 1}: Cost cannot be negative`);
        }
      });
    }

    // Risks validation (optional but validate if provided)
    if (risks && Array.isArray(risks) && risks.length > 0) {
      risks.forEach((risk, index) => {
        if (!risk.title || !risk.title.trim()) {
          errors.push(`Risk ${index + 1}: Title is required`);
        }
        if (!risk.description || !risk.description.trim()) {
          errors.push(`Risk ${index + 1}: Description is required`);
        }
        if (!risk.mitigation || !risk.mitigation.trim()) {
          errors.push(`Risk ${index + 1}: Mitigation strategy is required`);
        }
      });
    }

    // Assumptions validation (optional but validate if provided)
    if (assumptions && Array.isArray(assumptions) && assumptions.length > 0) {
      assumptions.forEach((assumption, index) => {
        if (!assumption.title || !assumption.title.trim()) {
          errors.push(`Assumption ${index + 1}: Title is required`);
        }
        if (!assumption.description || !assumption.description.trim()) {
          errors.push(`Assumption ${index + 1}: Description is required`);
        }
      });
    }

    // Return validation errors if any
    if (errors.length > 0) {
      return res.status(400).json({
        status: "failed",
        message: "Validation failed",
        errors: errors
      });
    }

    // Check if project exists
    const project = await Project.findById(projectId);
    if (!project) {
      return res.status(404).json({
        status: "failed",
        message: "Project not found"
      });
    }

    // Check if project plan already exists
    const existingPlan = await ProjectPlan.findOne({ project: projectId });
    if (existingPlan) {
      return res.status(400).json({
        status: "failed",
        message: "Project plan already exists for this project"
      });
    }

    // Create activities as separate documents and get their IDs
    const activityIds = [];
    if (activities && Array.isArray(activities)) {
      console.log(`📋 Creating ${activities.length} activities for project ${projectId}`);
      for (const activityData of activities) {
        console.log(`📝 Creating activity: ${activityData.title}`);
        const activity = await ProjectActivities.create({
          project: projectId,
          title: activityData.title,
          description: activityData.description,
          status: "pending",
          priority: "medium",
          createdBy: req.user.id
        });
        activityIds.push(activity._id);
        console.log(`✅ Activity created with ID: ${activity._id}`);
      }
    } else {
      console.log('⚠️ No activities provided or activities is not an array');
    }

    // Update project with activity references
    await Project.findByIdAndUpdate(projectId, {
      activities: activityIds
    });

    // Create project plan with the original activities data for the ProjectPlan model
    // The ProjectPlan model still stores activities as embedded documents for planning purposes
    const projectPlan = await ProjectPlan.create({
      project: projectId,
      expectedOutcome: "Comprehensive project plan with detailed activities, deliverables, milestones, resources, risks, and assumptions to achieve project objectives.", // Required field
      activities: activities || [], // Keep original structure for ProjectPlan
      deliverables: deliverables || [],
      milestones: milestones || [],
      resources: resources || [],
      risks: risks || [],
      assumptions: assumptions || [],
      status: "draft"
    });

    console.log(`✅ Created project plan with ${activityIds.length} activities as separate documents`);

    res.status(201).json({
      status: "success",
      message: "Project plan created successfully! You can now assign the project to a project manager.",
      data: {
        projectPlan,
        projectId
      }
    });

  } catch (error) {
    console.error("❌ Project plan creation error:", error);

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        status: "failed",
        message: "Validation failed",
        errors: validationErrors
      });
    }

    res.status(500).json({
      status: "failed",
      message: "Something went wrong while creating project plan",
      error: error.message
    });
  }
};

exports.assignProjectToManager = async (req, res) => {
  try {
    console.log("📝 Assigning project to manager with data:", req.body);
    const { projectId, projectManagerId } = req.body;

    // Validation
    const errors = [];

    if (!projectId || !projectId.trim()) {
      errors.push("Project ID is required");
    }

    if (!projectManagerId || !projectManagerId.trim()) {
      errors.push("Project Manager ID is required");
    }

    if (errors.length > 0) {
      return res.status(400).json({
        status: "failed",
        message: "Validation failed",
        errors: errors
      });
    }

    // Get project with plan
    const project = await Project.findById(projectId);
    const projectPlan = await ProjectPlan.findOne({ project: projectId });

    if (!project || !projectPlan) {
      return res.status(404).json({
        status: "failed",
        message: "Project or project plan not found"
      });
    }

    // Check if project is already assigned
    if (project.assignedTo) {
      return res.status(400).json({
        status: "failed",
        message: "Project is already assigned to a project manager"
      });
    }

    // Get project manager details
    const projectManager = await User.findById(projectManagerId);
    if (!projectManager) {
      return res.status(404).json({
        status: "failed",
        message: "Project manager not found"
      });
    }

    // Update project with assignment
    await Project.findByIdAndUpdate(projectId, {
      assignedTo: projectManagerId,
      status: "inprogress" // Set to inprogress when assigned to project manager
    });

    // Update project plan status
    await ProjectPlan.findOneAndUpdate(
      { project: projectId },
      { status: "submitted" }
    );

    // Send email notification to project manager with compiled details
    try {
      await sendProjectAssignmentMail(
        projectManager.email,
        project.title,
        project,
        projectPlan
      );
    } catch (emailError) {
      console.log("Email notification failed:", emailError);
    }

    // Send in-app notification
    try {
      await NotificationService.createNotification({
        recipient: projectManagerId,
        type: 'project_assigned',
        title: 'New Project Assigned',
        message: `You have been assigned to manage project: ${project.title}`,
        relatedEntity: {
          type: 'Project',
          id: projectId
        },
        actionUrl: `/manager/projects/${projectId}`
      });
    } catch (notificationError) {
      console.log("In-app notification failed:", notificationError);
    }

    res.status(200).json({
      status: "success",
      message: "Project successfully assigned to project manager with compiled details sent",
      data: {
        project,
        projectPlan,
        assignedTo: projectManager
      }
    });

  } catch (error) {
    console.error("❌ Project assignment error:", error);

    // Check if it's a validation error
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        status: "failed",
        message: "Validation failed",
        errors: validationErrors
      });
    }

    res.status(500).json({
      status: "failed",
      message: "Something went wrong while assigning project",
      error: error.message
    });
  }
};

exports.approveBudget = async (req, res) => {
  try {
    const { projectId } = req.params;

    const projectData = await Project.findById(projectId).populate("createdBy");

    if (projectData?.status !== "pending") {
      return res
        .status(400)
        .json({
          status: "failed",
          messgae: "You can not approve budget for this project",
        });
    }

    const approvedBudget = await Project.findByIdAndUpdate(
      projectId,
      { status: "active" },
      { $new: true }
    );

    if (!approvedBudget) {
      return res
        .status(404)
        .json({ status: "failed", message: "Project not found!!" });
    }

    await sendBudgetApprovalMail(
      projectData?.createdBy?.email,
      projectData?.name
    );

    // Send notification
    try {
      await NotificationService.notifyBudgetApproved(projectId, req.user._id);
    } catch (notificationError) {
      // Log error but don't fail the approval
    }

    res.status(200).json({
      status: "success",
      message: "Project budget approved successfully!!",
    });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.rejectBudget = async (req, res) => {
  try {
    const { projectId } = req.params;
    const { reason } = req.body;

    const project = await Project.findById(projectId);
    if (!project) {
      return res.status(404).json({ status: "failed", message: "Project not found" });
    }

    if (project.status !== "pending") {
      return res.status(400).json({ status: "failed", message: "Cannot reject a non-pending project" });
    }

    project.status = "rejected";
    project.rejectionReason = reason; // ✅ Make sure this field exists in your Project schema
    await project.save();

    // Send notification
    try {
      await NotificationService.notifyBudgetRejected(projectId, req.user._id, reason);
    } catch (notificationError) {
      // Log error but don't fail the rejection
    }

    res.status(200).json({ status: "success", message: "Budget rejected successfully" });
  } catch (err) {
    res.status(500).json({ status: "failed", message: "Internal server error" });
  }
};


exports.getAllProjects = async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role?.name;

    let filter = {};

    // Filter projects based on user role
    if (userRole === 'projectManager') {
      // Project managers can only see projects assigned to them
      filter.assignedTo = userId;
    } else if (userRole === 'seniorManager' || userRole === 'admin') {
      // Senior managers and admins can see all projects
      filter = {};
    } else if (userRole === 'accountant') {
      // Accountants can see all projects for budget management
      filter = {};
    } else {
      // Other roles get no projects by default
      filter._id = null; // This will return no results
    }

    const projects = await Project.find(filter)
      .populate("assignedTo", "fullName email")
      .populate("location", "name")
      .populate("createdBy", "fullName email")
      .populate("objectives", "description status priority targetDate")
      .populate("goals", "description status priority targetDate measurableOutcome")
      .populate("activities", "title description status priority startDate endDate assignedTo");

    // Add computed fields for each project
    const projectsWithComputedData = await Promise.all(
      projects.map(async (project) => {
        // Get activity count for team size
        const totalActivities = await ProjectActivities.countDocuments({
          project: project._id
        });

        const assignedActivities = await ProjectActivities.countDocuments({
          project: project._id,
          assignedTo: { $exists: true }
        });

        // Convert to plain object and add computed fields
        const projectObj = project.toObject();

        return {
          ...projectObj,
          progress: projectObj.progressPercentage || 0,
          teamSize: assignedActivities,
          budget: {
            total: projectObj.initialBudget || 0,
            used: projectObj.usedBudget || 0,
            remaining: projectObj.remainingBudget || projectObj.initialBudget || 0,
            utilization: projectObj.budgetUtilization || 0
          }
        };
      })
    );

    res.status(200).json({ status: "success", projects: projectsWithComputedData });
  } catch (error) {
    console.error("Get all projects error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again",
    });
  }
};

exports.assignProjectToPM = async (req, res) => {
  try {
    const { projectId, projectManagerId } = req.params;

    const projectData = await Project.findById(projectId).populate("createdBy");

    if (projectData?.assignedTo) {
      return res
        .status(400)
        .json({ status: "failed", messgae: "You can not assign this project" });
    }

    const assignedproject = await Project.findByIdAndUpdate(
      projectId,
      { assignedTo: projectManagerId },
      { $new: true }
    );

    if (!assignedproject) {
      return res
        .status(404)
        .json({ status: "failed", message: "Project not found!" });
    }

    const projectManagerData = await User.findById(projectManagerId);

    await sendProjectAssignmentMail(
      projectManagerData?.email,
      assignedproject?.name
    );

    res.status(200).json({
      status: "success",
      message: "Project has been assigned to Project manager successfully!!",
    });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! PLease try again",
    });
  }
};

exports.getProjectProgress = async (req, res) => {
  try {
    const { projectId } = req.query;

    let projects;
    if (projectId) {
      projects = await Project.findById(projectId).populate('location', 'name');
    } else {
      projects = await Project.find().populate('location', 'name');
    }

    if (!projects) {
      return res.status(404).json({
        status: "failed",
        message: "No projects found"
      });
    }

    const projectsArray = Array.isArray(projects) ? projects : [projects];
    const progressData = [];

    for (const project of projectsArray) {
      let progress = 0;
      let milestonesProgress = [];
      let activitiesProgress = [];
      let deliverablesProgress = [];

      // Get actual project activities from ProjectActivities collection
      const projectActivities = await ProjectActivities.find({ project: project._id })
        .populate('assignedTo', 'fullName email')
        .populate('assignedBy', 'fullName email')
        .sort({ createdAt: 1 });

      // Calculate activities progress based on actual activity status
      if (projectActivities && projectActivities.length > 0) {
        const completedActivities = projectActivities.filter(a => a.status === 'completed').length;
        const inProgressActivities = projectActivities.filter(a => a.status === 'inprogress').length;
        const totalActivities = projectActivities.length;

        // Calculate weighted progress (completed = 100%, in-progress = 50%, pending = 0%)
        const weightedProgress = (completedActivities * 100) + (inProgressActivities * 50);
        progress = totalActivities > 0 ? Math.round(weightedProgress / (totalActivities * 100) * 100) : 0;

        activitiesProgress = projectActivities.map(activity => {
          let activityProgress = 0;
          if (activity.status === 'completed') {
            activityProgress = 100;
          } else if (activity.status === 'inprogress') {
            activityProgress = 50;
          }

          return {
            title: activity.title,
            description: activity.description,
            progress: activityProgress,
            status: activity.status,
            startDate: activity.startDate,
            endDate: activity.endDate,
            assignedTo: activity.assignedTo?.fullName,
            priority: activity.priority,
            budget: activity.budget || 0
          };
        });
      }

      // Get project plan for milestones and deliverables
      const projectPlan = await ProjectPlan.findOne({ project: project._id });

      if (projectPlan) {
        // Calculate milestone progress based on project activities completion
        if (projectPlan.milestones && projectPlan.milestones.length > 0) {
          milestonesProgress = projectPlan.milestones.map(milestone => {
            const deadline = new Date(milestone.deadline);
            const today = new Date();
            const isOverdue = deadline < today;

            let completed = false;
            let progress = 0;
            let status = milestone.status || 'pending';

            // NEW LOGIC: Milestones are marked complete when all project activities are completed
            const totalActivities = projectActivities.length;
            const completedActivities = projectActivities.filter(a => a.status === 'completed').length;
            const inProgressActivities = projectActivities.filter(a => a.status === 'inprogress').length;

            if (totalActivities > 0) {
              // Calculate milestone progress based on activity completion
              const activityCompletionRate = completedActivities / totalActivities;
              const weightedProgress = (completedActivities * 100) + (inProgressActivities * 50);
              progress = Math.round(weightedProgress / (totalActivities * 100) * 100);

              // Mark milestone as completed when all activities are completed
              if (completedActivities === totalActivities && totalActivities > 0) {
                completed = true;
                progress = 100;
                status = 'completed';
              } else if (inProgressActivities > 0 || completedActivities > 0) {
                status = 'inprogress';
              } else if (isOverdue) {
                status = 'delayed';
              } else {
                status = 'pending';
              }
            } else {
              // No activities yet - check original milestone status or mark as pending
              if (milestone.status === 'completed') {
                completed = true;
                progress = 100;
                status = 'completed';
              } else if (isOverdue) {
                status = 'delayed';
              } else {
                status = 'pending';
              }
            }

            return {
              title: milestone.title,
              description: milestone.description,
              deadline: milestone.deadline,
              targetDate: milestone.deadline,
              completedDate: milestone.completedDate,
              completed: completed,
              progress: progress,
              status: status,
              isOverdue: isOverdue && !completed,
              dependencies: milestone.dependencies,
              deliverables: milestone.deliverables,
              assignedTo: milestone.assignedTo,
              priority: milestone.priority,
              successCriteria: milestone.successCriteria
            };
          });
        }

        // Calculate deliverables progress based on project plan
        if (projectPlan.deliverables && projectPlan.deliverables.length > 0) {
          deliverablesProgress = projectPlan.deliverables.map((deliverable, index) => {
            const deadline = new Date(deliverable.deadline);
            const today = new Date();
            const isOverdue = deadline < today;

            let completed = false;
            let progress = 0;
            let status = deliverable.status || 'pending';

            // Deliverables are marked complete when the project is complete
            if (project.status === 'completed') {
              completed = true;
              progress = 100;
              status = 'completed';
            } else {
              // Use deliverable status from project plan
              if (deliverable.status === 'approved') {
                completed = true;
                progress = 100;
                status = 'approved';
              } else if (deliverable.status === 'submitted') {
                progress = 75;
                status = 'submitted';
              } else if (deliverable.status === 'inprogress') {
                progress = 50;
                status = 'inprogress';
              } else if (isOverdue) {
                status = 'overdue';
              } else {
                status = 'pending';
              }
            }

            return {
              title: deliverable.title,
              description: deliverable.description,
              deadline: deliverable.deadline,
              type: deliverable.type,
              assignedTo: deliverable.assignedTo,
              requirements: deliverable.requirements,
              completed: completed,
              progress: progress,
              status: status,
              isOverdue: isOverdue && !completed,
              submittedFiles: []
            };
          });
        }
      }

      // Calculate milestone statistics
      const totalMilestones = milestonesProgress.length;
      const completedMilestones = milestonesProgress.filter(m => m.status === 'completed').length;
      const upcomingMilestones = milestonesProgress.filter(m => m.status === 'pending').length;
      const overdueMilestones = milestonesProgress.filter(m => m.status === 'delayed').length;

      // Calculate deliverable statistics
      const totalDeliverables = deliverablesProgress.length;
      const completedDeliverables = deliverablesProgress.filter(d => d.status === 'completed' || d.status === 'approved').length;
      const inProgressDeliverables = deliverablesProgress.filter(d => d.status === 'inprogress' || d.status === 'submitted').length;
      const overdueDeliverables = deliverablesProgress.filter(d => d.status === 'overdue').length;

      progressData.push({
        projectId: project._id,
        title: project.title,
        name: project.title, // For backward compatibility
        progress: Math.round(progress),
        status: project.status,
        startDate: project.startDate,
        endDate: project.endDate,
        location: project.location?.name,
        milestones: milestonesProgress,
        activities: activitiesProgress,
        deliverables: deliverablesProgress,
        totalActivities: projectActivities.length,
        completedActivities: projectActivities.filter(a => a.status === 'completed').length,
        inProgressActivities: projectActivities.filter(a => a.status === 'inprogress').length,
        pendingActivities: projectActivities.filter(a => a.status === 'pending').length,
        // Milestone statistics
        totalMilestones,
        completedMilestones,
        upcomingMilestones,
        overdueMilestones,
        // Deliverable statistics
        totalDeliverables,
        completedDeliverables,
        inProgressDeliverables,
        overdueDeliverables
      });
    }

    res.status(200).json({
      status: "success",
      data: progressData
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again",
    });
  }
};

exports.getBudgetOverview = async (req, res) => {
  try {
    console.log('🔍 Budget Overview Request:');
    console.log('   User:', req.user?.fullName);
    console.log('   User Role:', req.user?.role?.name);
    console.log('   Query params:', req.query);

    const { projectId } = req.query;

    let query = {};
    if (projectId) {
      query._id = projectId;
    }

    console.log('   Database query:', query);

    // Get project(s) with budget information
    const projects = await Project.find(query);

    if (projects.length === 0) {
      return res.status(404).json({
        status: "failed",
        message: "No projects found"
      });
    }

    // Calculate total budget used and remaining
    let totalBudget = 0;
    let totalUsed = 0;
    let totalRemaining = 0;

    projects.forEach(project => {
      totalBudget += project.budget || 0;
      totalUsed += (project.budget || 0) - (project.remainingBudget || 0);
      totalRemaining += project.remainingBudget || 0;
    });

    res.status(200).json({
      status: "success",
      data: {
        used: totalUsed,
        remaining: totalRemaining
      }
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again",
    });
  }
};

exports.getRecentProjects = async (req, res) => {
  try {
    const recentProjects = await Project.find()
      .sort({ createdAt: -1 }) 
      .limit(5)
      .populate('assignedTo'); 

    res.status(200).json({ status: 'success', data: recentProjects });
  } catch (err) {
    res.status(500).json({ status: 'failed', message: 'Could not fetch recent projects' });
  }
};

// Helper function to create project objectives
exports.createObjective = async (req, res) => {
  try {
    const { project, description, priority, targetDate } = req.body;
    const createdBy = req.user.id;

    const objective = await ProjectObjective.create({
      project,
      description,
      priority: priority || "medium",
      targetDate,
      createdBy,
      status: "pending"
    });

    const populatedObjective = await ProjectObjective.findById(objective._id)
      .populate('project', 'title')
      .populate('createdBy', 'fullName email');

    res.status(201).json({
      status: "success",
      message: "Objective created successfully",
      data: populatedObjective
    });
  } catch (error) {
    console.error("Create objective error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again",
    });
  }
};

// Helper function to create project goals
exports.createGoal = async (req, res) => {
  try {
    const { project, description, priority, targetDate, measurableOutcome } = req.body;
    const createdBy = req.user.id;

    const goal = await ProjectGoal.create({
      project,
      description,
      priority: priority || "medium",
      targetDate,
      measurableOutcome,
      createdBy,
      status: "pending"
    });

    const populatedGoal = await ProjectGoal.findById(goal._id)
      .populate('project', 'title')
      .populate('createdBy', 'fullName email');

    res.status(201).json({
      status: "success",
      message: "Goal created successfully",
      data: populatedGoal
    });
  } catch (error) {
    console.error("Create goal error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again",
    });
  }
};

// Migration endpoint to update project statuses
exports.migrateProjectStatuses = async (req, res) => {
  try {
    console.log('🔄 Starting project status migration...');

    // Find all projects with old status values
    const projectsToUpdate = await Project.find({
      status: { $in: ['draft', 'planning', 'assigned', 'active', 'rejected'] }
    });

    console.log(`📊 Found ${projectsToUpdate.length} projects to migrate`);

    if (projectsToUpdate.length === 0) {
      return res.status(200).json({
        status: "success",
        message: "No projects need migration",
        data: { migratedCount: 0 }
      });
    }

    // Update projects based on their current status
    const updatePromises = projectsToUpdate.map(async (project) => {
      let newStatus = 'inprogress'; // Default to inprogress

      // Map old statuses to new ones
      switch (project.status) {
        case 'draft':
        case 'planning':
        case 'assigned':
        case 'active':
          newStatus = 'inprogress';
          break;
        case 'rejected':
          newStatus = 'completed'; // For rejected projects
          break;
        default:
          newStatus = 'inprogress';
      }

      console.log(`📝 Updating project "${project.title}" from "${project.status}" to "${newStatus}"`);

      return Project.findByIdAndUpdate(
        project._id,
        { status: newStatus },
        { new: true }
      );
    });

    // Execute all updates
    const updatedProjects = await Promise.all(updatePromises);

    console.log(`✅ Successfully migrated ${updatedProjects.length} projects`);

    res.status(200).json({
      status: "success",
      message: `Successfully migrated ${updatedProjects.length} projects`,
      data: {
        migratedCount: updatedProjects.length,
        projects: updatedProjects.map(p => ({ id: p._id, title: p.title, newStatus: p.status }))
      }
    });

  } catch (error) {
    console.error("Migration error:", error);
    res.status(500).json({
      status: "failed",
      message: "Migration failed",
      error: error.message
    });
  }
};

// Create sample data for testing (development only)
exports.createSampleData = async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        status: "failed",
        message: "Sample data creation not allowed in production"
      });
    }

    console.log('🔄 Creating sample data...');

    // Get the current user
    const createdBy = req.user.id;

    // Create sample projects
    const sampleProjects = [
      {
        title: "Water Supply Project",
        description: "Providing clean water access to rural communities",
        beneficiaries: "Rural communities in Lilongwe district with limited access to clean water",
        category: "Infrastructure",
        initialBudget: 500000,
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        status: "inprogress",
        attachment: "sample-approval.pdf",
        createdBy
      },
      {
        title: "Education Enhancement Program",
        description: "Improving educational facilities and resources",
        beneficiaries: "Students and teachers in primary schools across Blantyre district",
        category: "Education",
        initialBudget: 750000,
        startDate: new Date(),
        endDate: new Date(Date.now() + 300 * 24 * 60 * 60 * 1000), // 10 months from now
        status: "inprogress",
        attachment: "sample-approval.pdf",
        createdBy
      }
    ];

    const createdProjects = [];

    for (const projectData of sampleProjects) {
      // Create project
      const project = await Project.create(projectData);

      // Create sample objectives
      const objective1 = await ProjectObjective.create({
        project: project._id,
        description: `Primary objective for ${project.title}`,
        priority: "high",
        status: "pending",
        createdBy
      });

      const objective2 = await ProjectObjective.create({
        project: project._id,
        description: `Secondary objective for ${project.title}`,
        priority: "medium",
        status: "inprogress",
        createdBy
      });

      // Create sample goals
      const goal1 = await ProjectGoal.create({
        project: project._id,
        description: `Main goal for ${project.title}`,
        priority: "high",
        status: "pending",
        createdBy
      });

      // Create sample activities
      const activity1 = await ProjectActivities.create({
        project: project._id,
        title: `Planning phase for ${project.title}`,
        description: "Initial planning and resource allocation",
        status: "completed",
        priority: "high",
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        budget: 50000,
        createdBy
      });

      const activity2 = await ProjectActivities.create({
        project: project._id,
        title: `Implementation phase for ${project.title}`,
        description: "Main implementation activities",
        status: "inprogress",
        priority: "high",
        startDate: new Date(),
        endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
        budget: 200000,
        createdBy
      });

      const activity3 = await ProjectActivities.create({
        project: project._id,
        title: `Monitoring phase for ${project.title}`,
        description: "Monitoring and evaluation activities",
        status: "pending",
        priority: "medium",
        startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
        budget: 75000,
        createdBy
      });

      // Update project with references
      project.objectives = [objective1._id, objective2._id];
      project.goals = [goal1._id];
      project.activities = [activity1._id, activity2._id, activity3._id];
      await project.save();

      createdProjects.push(project);
    }

    console.log(`✅ Created ${createdProjects.length} sample projects with activities`);

    res.status(201).json({
      status: "success",
      message: `Created ${createdProjects.length} sample projects with objectives, goals, and activities`,
      data: {
        projects: createdProjects.map(p => ({
          id: p._id,
          title: p.title,
          status: p.status
        }))
      }
    });

  } catch (error) {
    console.error("Sample data creation error:", error);
    res.status(500).json({
      status: "failed",
      message: "Failed to create sample data",
      error: error.message
    });
  }
};

// 🟦 Get activities for a project (for project manager and senior manager)
exports.getProjectActivities = async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    let project;

    // Senior managers can access all project activities
    if (userRole === 'seniorManager') {
      // Verify the project exists
      project = await Project.findById(projectId);
      if (!project) {
        return res.status(404).json({
          status: "failed",
          message: "Project not found"
        });
      }
    } else {
      // For project managers, verify they are assigned to this project
      project = await Project.findOne({
        _id: projectId,
        assignedTo: userId
      });

      if (!project) {
        return res.status(403).json({
          status: "failed",
          message: "You are not authorized to access activities for this project"
        });
      }
    }

    // Get all activities for this project
    console.log(`🔍 Searching for activities for project: ${projectId}`);
    const activities = await ProjectActivities.find({ project: projectId })
      .populate('assignedTo', 'fullName email role')
      .populate('assignedBy', 'fullName email')
      .populate('createdBy', 'fullName email')
      .sort({ createdAt: -1 });

    console.log(`📋 Found ${activities.length} activities for project ${projectId}`);
    if (activities.length > 0) {
      console.log('📝 Sample activity:', {
        id: activities[0]._id,
        title: activities[0].title,
        status: activities[0].status,
        createdBy: activities[0].createdBy?.fullName
      });
    }

    res.status(200).json({
      status: "success",
      message: "Activities retrieved successfully",
      data: {
        project: {
          id: project._id,
          title: project.title,
          status: project.status
        },
        activities: activities
      }
    });

  } catch (error) {
    console.error("Get project activities error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching activities"
    });
  }
};

// 🟦 Assign activity to field officer (project manager only)
exports.assignActivityToFieldOfficer = async (req, res) => {
  try {
    console.log('🔄 Activity assignment request received');
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));
    console.log('📋 Activity ID:', req.params.activityId);

    const { activityId } = req.params;
    const {
      assignedTo, // Field officer ID
      startDate,
      endDate,
      budget,
      location,
      targetOutcome,
      priority,
      kpis
    } = req.body;

    const projectManagerId = req.user.id;

    // Validate required fields
    if (!assignedTo) {
      console.log('❌ Validation failed: assignedTo is required');
      return res.status(400).json({
        status: "failed",
        message: "Field officer assignment is required"
      });
    }

    if (!startDate || !endDate) {
      console.log('❌ Validation failed: dates are required');
      return res.status(400).json({
        status: "failed",
        message: "Start date and end date are required"
      });
    }

    // Get the activity and verify project manager has access
    const activity = await ProjectActivities.findById(activityId).populate('project');

    if (!activity) {
      return res.status(404).json({
        status: "failed",
        message: "Activity not found"
      });
    }

    // Verify the project manager is assigned to this project
    const project = await Project.findOne({
      _id: activity.project._id,
      assignedTo: projectManagerId
    });

    if (!project) {
      return res.status(403).json({
        status: "failed",
        message: "You are not authorized to assign activities for this project"
      });
    }

    // Verify the assigned user is a field officer
    const fieldOfficer = await User.findById(assignedTo).populate('role');
    if (!fieldOfficer || fieldOfficer.role.name !== 'fieldOfficer') {
      return res.status(400).json({
        status: "failed",
        message: "Activity can only be assigned to field officers"
      });
    }

    // Validate dates
    console.log('📅 Validating dates - Start:', startDate, 'End:', endDate);
    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    console.log('📅 Parsed dates - Start:', start, 'End:', end, 'Today:', today);

    if (isNaN(start.getTime())) {
      return res.status(400).json({
        status: "failed",
        message: "Invalid start date format"
      });
    }

    if (isNaN(end.getTime())) {
      return res.status(400).json({
        status: "failed",
        message: "Invalid end date format"
      });
    }

    if (start < today) {
      console.log('❌ Start date is in the past');
      return res.status(400).json({
        status: "failed",
        message: "Start date cannot be in the past"
      });
    }

    if (end <= start) {
      console.log('❌ End date is not after start date');
      return res.status(400).json({
        status: "failed",
        message: "End date must be after start date"
      });
    }

    console.log('✅ Date validation passed');

    // Process KPIs if provided
    let processedKpis = [];
    console.log('📊 Processing KPIs:', kpis);

    if (kpis && Array.isArray(kpis)) {
      console.log('📊 KPIs is array with length:', kpis.length);

      // Validate and process KPIs
      for (const kpi of kpis) {
        if (!kpi.name || !kpi.name.trim()) {
          return res.status(400).json({
            status: "failed",
            message: "KPI name is required for all KPIs"
          });
        }
        if (!kpi.target || !kpi.target.trim()) {
          return res.status(400).json({
            status: "failed",
            message: "KPI target is required for all KPIs"
          });
        }

        processedKpis.push({
          name: kpi.name.trim(),
          target: kpi.target.trim(),
          description: kpi.description ? kpi.description.trim() : ""
        });
      }

      console.log('📊 Processed KPIs:', processedKpis);
    } else {
      console.log('📊 No KPIs provided or not an array');
    }

    // Update the activity with assignment details
    console.log('💾 Updating activity with data:', {
      assignedTo,
      assignedBy: projectManagerId,
      startDate,
      endDate,
      budget: budget || 0,
      location: location || "",
      targetOutcome: targetOutcome || "",
      priority: priority || "medium",
      kpis: processedKpis
    });

    let updatedActivity;
    try {
      updatedActivity = await ProjectActivities.findByIdAndUpdate(
        activityId,
        {
          assignedTo,
          assignedBy: projectManagerId,
          assignmentDate: new Date(),
          startDate,
          endDate,
          budget: budget || 0,
          location: location || "",
          targetOutcome: targetOutcome || "",
          priority: priority || "medium",
          kpis: processedKpis,
          status: "pending" // Reset to pending when assigned
        },
        { new: true, runValidators: true }
      ).populate('assignedTo', 'fullName email')
       .populate('assignedBy', 'fullName email');

      console.log('✅ Activity updated successfully');
       console.log('📊 Updated activity KPIs:', updatedActivity.kpis);
    } catch (updateError) {
      console.error('❌ Database update error:', updateError);
      console.error('❌ Update error details:', updateError.message);
      if (updateError.name === 'ValidationError') {
        console.error('❌ Validation errors:', updateError.errors);
      }
      return res.status(400).json({
        status: "failed",
        message: "Failed to update activity: " + updateError.message,
        error: updateError.name === 'ValidationError' ? updateError.errors : updateError.message
      });
    }

    // Send notification to field officer
    try {
      await NotificationService.createNotification({
        recipient: assignedTo,
        type: 'activity_assigned',
        title: 'New Activity Assigned',
        message: `You have been assigned a new activity: ${activity.title}`,
        relatedModel: 'ProjectActivities',
        relatedId: activityId,
        createdBy: projectManagerId
      });
    } catch (notificationError) {
      console.error("Notification error:", notificationError);
      // Don't fail the request if notification fails
    }

    // Send email notification to field officer
    try {
      if (updatedActivity.assignedTo && updatedActivity.assignedTo.email) {
        await sendDetailedActivityAssignmentMail({
          to: updatedActivity.assignedTo.email,
          fieldOfficerName: updatedActivity.assignedTo.fullName,
          projectName: activity.project.name,
          activityTitle: activity.title,
          activityDescription: activity.description,
          startDate: startDate,
          endDate: endDate,
          budget: budget,
          location: location,
          targetOutcome: targetOutcome,
          priority: priority,
          kpis: processedKpis,
          assignedByName: updatedActivity.assignedBy.fullName
        });
        console.log('✅ Detailed email notification sent to field officer');
      }
    } catch (emailError) {
      console.error('❌ Failed to send email notification:', emailError);
      // Don't fail the request if email fails
    }

    // Notify accountants about activity budget assignment
    try {
      const NotificationService = require('../services/notificationService');
      await NotificationService.notifyActivityBudgetAssignment(activityId, projectManagerId);
      console.log('✅ Activity budget assignment notification sent to accountants');
    } catch (notificationError) {
      console.error('❌ Failed to send activity budget assignment notification:', notificationError);
      // Don't fail the request if notification fails
    }

    res.status(200).json({
      status: "success",
      message: "Activity assigned successfully",
      data: updatedActivity
    });

  } catch (error) {
    console.error("Assign activity error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while assigning activity"
    });
  }
};

// 🟦 Update activity details (project manager only)
exports.updateActivityDetails = async (req, res) => {
  try {
    const { activityId } = req.params;
    const updateData = req.body;
    const projectManagerId = req.user.id;

    // Get the activity and verify project manager has access
    const activity = await ProjectActivities.findById(activityId).populate('project');

    if (!activity) {
      return res.status(404).json({
        status: "failed",
        message: "Activity not found"
      });
    }

    // Verify the project manager is assigned to this project
    const project = await Project.findOne({
      _id: activity.project._id,
      assignedTo: projectManagerId
    });

    if (!project) {
      return res.status(403).json({
        status: "failed",
        message: "You are not authorized to update activities for this project"
      });
    }

    // Remove fields that shouldn't be updated directly
    delete updateData.project;
    delete updateData.createdBy;
    delete updateData._id;

    // Update the activity
    const updatedActivity = await ProjectActivities.findByIdAndUpdate(
      activityId,
      updateData,
      { new: true, runValidators: true }
    ).populate('assignedTo', 'fullName email')
     .populate('assignedBy', 'fullName email');

    res.status(200).json({
      status: "success",
      message: "Activity updated successfully",
      data: updatedActivity
    });

  } catch (error) {
    console.error("Update activity error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong while updating activity"
    });
  }
};

// Test endpoint to check if basic functionality is working
exports.testEndpoint = async (req, res) => {
  try {
    console.log("🧪 Test endpoint called with:", {
      method: req.method,
      body: req.body,
      user: req.user ? { id: req.user.id, role: req.user.role?.name } : 'No user',
      headers: req.headers.authorization ? 'Has auth header' : 'No auth header'
    });

    res.status(200).json({
      status: "success",
      message: "Test endpoint working",
      data: {
        receivedData: req.body,
        user: req.user ? { id: req.user.id, role: req.user.role?.name } : null,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error("Test endpoint error:", error);
    res.status(500).json({
      status: "failed",
      message: "Test endpoint failed",
      error: error.message
    });
  }
};

// 🔧 Migration endpoint to add KPIs field to existing activities
exports.migrateKPIs = async (req, res) => {
  try {
    console.log('🔄 Starting KPIs migration...');

    // Find all activities that don't have the kpis field or have null/undefined kpis
    const activitiesWithoutKPIs = await ProjectActivities.find({
      $or: [
        { kpis: { $exists: false } },
        { kpis: null },
        { kpis: undefined }
      ]
    });

    console.log(`📊 Found ${activitiesWithoutKPIs.length} activities without KPIs field`);

    if (activitiesWithoutKPIs.length === 0) {
      return res.status(200).json({
        status: "success",
        message: "All activities already have KPIs field",
        data: { updated: 0, total: await ProjectActivities.countDocuments() }
      });
    }

    // Update all activities to have an empty kpis array
    const result = await ProjectActivities.updateMany(
      {
        $or: [
          { kpis: { $exists: false } },
          { kpis: null },
          { kpis: undefined }
        ]
      },
      {
        $set: { kpis: [] }
      }
    );

    console.log(`✅ Migration completed: ${result.modifiedCount} activities updated`);

    // Verify the migration
    const verifyCount = await ProjectActivities.countDocuments({ kpis: { $exists: true } });
    const totalCount = await ProjectActivities.countDocuments();

    res.status(200).json({
      status: "success",
      message: "KPIs migration completed successfully",
      data: {
        updated: result.modifiedCount,
        verified: verifyCount,
        total: totalCount
      }
    });

  } catch (error) {
    console.error('❌ Migration failed:', error);
    res.status(500).json({
      status: "failed",
      message: "Migration failed: " + error.message
    });
  }
};

