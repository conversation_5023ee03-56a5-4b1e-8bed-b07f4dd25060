const Project = require("../models/project.model");
const ProjectActivities = require("../models/projectActivities.model");
const ActivityReport = require("../models/activityReport.model");

class ProjectTrackingService {
  // Determine if a project is on track based on multiple factors
  static async evaluateProjectStatus(projectId) {
    try {
      const project = await Project.findById(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      const activities = await ProjectActivities.find({ project: projectId });
      const reports = await ActivityReport.find({
        activity: { $in: activities.map(a => a._id) }
      });

      // Calculate various tracking metrics
      const timeProgress = this.calculateTimeProgress(project);
      const activityProgress = this.calculateActivityProgress(activities, reports);
      const budgetProgress = this.calculateBudgetProgress(project);
      const qualityMetrics = this.calculateQualityMetrics(activities, reports);

      // Determine overall status
      const overallStatus = this.determineOverallStatus({
        timeProgress,
        activityProgress,
        budgetProgress,
        qualityMetrics
      });

      return {
        projectId,
        projectName: project.name,
        overallStatus,
        statusColor: this.getStatusColor(overallStatus),
        metrics: {
          timeProgress,
          activityProgress,
          budgetProgress,
          qualityMetrics
        },
        recommendations: this.generateRecommendations({
          timeProgress,
          activityProgress,
          budgetProgress,
          qualityMetrics,
          project,
          activities
        }),
        alerts: this.generateAlerts({
          timeProgress,
          activityProgress,
          budgetProgress,
          qualityMetrics,
          project
        })
      };
    } catch (error) {
      throw error;
    }
  }

  // Calculate time-based progress
  static calculateTimeProgress(project) {
    const now = new Date();
    const startDate = new Date(project.startDate);
    const endDate = new Date(project.endDate);
    
    const totalDuration = endDate - startDate;
    const elapsedTime = now - startDate;
    const remainingTime = endDate - now;
    
    const timeElapsedPercentage = totalDuration > 0 ? Math.max(0, Math.min(100, (elapsedTime / totalDuration) * 100)) : 0;
    const isOverdue = now > endDate;
    const daysRemaining = Math.ceil(remainingTime / (1000 * 60 * 60 * 24));
    
    return {
      timeElapsedPercentage: Math.round(timeElapsedPercentage),
      daysRemaining,
      isOverdue,
      status: isOverdue ? 'overdue' : daysRemaining < 30 ? 'approaching_deadline' : 'on_time'
    };
  }

  // Calculate activity-based progress
  static calculateActivityProgress(activities, reports) {
    const totalActivities = activities.length;
    const completedActivities = activities.filter(a => a.status === 'completed').length;
    const inProgressActivities = activities.filter(a => a.status === 'in-progress').length;
    const pendingActivities = activities.filter(a => a.status === 'pending').length;
    
    const activitiesWithReports = reports.length;
    const onTimeReports = reports.filter(r => r.status === 'onTime').length;
    const lateReports = reports.filter(r => r.status === 'lateSubmission').length;
    
    const completionPercentage = totalActivities > 0 ? (completedActivities / totalActivities) * 100 : 0;
    const reportingRate = totalActivities > 0 ? (activitiesWithReports / totalActivities) * 100 : 0;
    const onTimeRate = activitiesWithReports > 0 ? (onTimeReports / activitiesWithReports) * 100 : 100;
    
    // Check for overdue activities
    const now = new Date();
    const overdueActivities = activities.filter(a => 
      a.status !== 'completed' && new Date(a.dueDate) < now
    ).length;
    
    return {
      totalActivities,
      completedActivities,
      inProgressActivities,
      pendingActivities,
      completionPercentage: Math.round(completionPercentage),
      reportingRate: Math.round(reportingRate),
      onTimeRate: Math.round(onTimeRate),
      overdueActivities,
      status: this.getActivityStatus(completionPercentage, onTimeRate, overdueActivities)
    };
  }

  // Calculate budget-based progress
  static calculateBudgetProgress(project) {
    const totalBudget = project.budget || 0;
    const remainingBudget = project.remainingBudget || 0;
    const usedBudget = totalBudget - remainingBudget;
    
    const budgetUtilization = totalBudget > 0 ? (usedBudget / totalBudget) * 100 : 0;
    const isOverBudget = remainingBudget < 0;
    const budgetVariance = remainingBudget;
    
    return {
      totalBudget,
      usedBudget,
      remainingBudget,
      budgetUtilization: Math.round(budgetUtilization),
      isOverBudget,
      budgetVariance,
      status: this.getBudgetStatus(budgetUtilization, isOverBudget)
    };
  }

  // Calculate quality metrics
  static calculateQualityMetrics(activities, reports) {
    const totalReports = reports.length;
    const approvedReports = reports.filter(r => r.approved).length;
    const rejectedReports = totalReports - approvedReports;
    
    const qualityScore = totalReports > 0 ? (approvedReports / totalReports) * 100 : 100;
    const averageLateDays = this.calculateAverageLateDays(reports);
    
    return {
      totalReports,
      approvedReports,
      rejectedReports,
      qualityScore: Math.round(qualityScore),
      averageLateDays,
      status: this.getQualityStatus(qualityScore, averageLateDays)
    };
  }

  // Determine overall project status
  static determineOverallStatus({ timeProgress, activityProgress, budgetProgress, qualityMetrics }) {
    const statuses = [
      timeProgress.status,
      activityProgress.status,
      budgetProgress.status,
      qualityMetrics.status
    ];
    
    // Priority: critical issues first
    if (statuses.includes('critical')) return 'critical';
    if (statuses.includes('at_risk')) return 'at_risk';
    if (statuses.includes('warning')) return 'warning';
    if (statuses.includes('on_track')) return 'on_track';
    
    return 'unknown';
  }

  // Helper methods for status determination
  static getActivityStatus(completionPercentage, onTimeRate, overdueActivities) {
    if (overdueActivities > 0 || onTimeRate < 50) return 'critical';
    if (completionPercentage < 25 || onTimeRate < 75) return 'at_risk';
    if (completionPercentage < 50 || onTimeRate < 90) return 'warning';
    return 'on_track';
  }

  static getBudgetStatus(budgetUtilization, isOverBudget) {
    if (isOverBudget) return 'critical';
    if (budgetUtilization > 90) return 'at_risk';
    if (budgetUtilization > 75) return 'warning';
    return 'on_track';
  }

  static getQualityStatus(qualityScore, averageLateDays) {
    if (qualityScore < 60 || averageLateDays > 7) return 'critical';
    if (qualityScore < 80 || averageLateDays > 3) return 'at_risk';
    if (qualityScore < 90 || averageLateDays > 1) return 'warning';
    return 'on_track';
  }

  static getStatusColor(status) {
    const colors = {
      'on_track': '#10B981', // Green
      'warning': '#F59E0B',  // Yellow
      'at_risk': '#F97316',  // Orange
      'critical': '#EF4444', // Red
      'unknown': '#6B7280'   // Gray
    };
    return colors[status] || colors.unknown;
  }

  static calculateAverageLateDays(reports) {
    const lateReports = reports.filter(r => r.lateDays > 0);
    if (lateReports.length === 0) return 0;
    
    const totalLateDays = lateReports.reduce((sum, r) => sum + r.lateDays, 0);
    return Math.round(totalLateDays / lateReports.length);
  }

  // Generate recommendations based on project status
  static generateRecommendations({ timeProgress, activityProgress, budgetProgress, qualityMetrics, project, activities }) {
    const recommendations = [];
    
    // Time-based recommendations
    if (timeProgress.status === 'approaching_deadline') {
      recommendations.push({
        type: 'time',
        priority: 'medium',
        message: `Project deadline is approaching in ${timeProgress.daysRemaining} days. Consider accelerating activities.`
      });
    } else if (timeProgress.status === 'overdue') {
      recommendations.push({
        type: 'time',
        priority: 'high',
        message: 'Project is overdue. Immediate action required to get back on track.'
      });
    }
    
    // Activity-based recommendations
    if (activityProgress.overdueActivities > 0) {
      recommendations.push({
        type: 'activity',
        priority: 'high',
        message: `${activityProgress.overdueActivities} activities are overdue. Review and reassign if necessary.`
      });
    }
    
    if (activityProgress.completionPercentage < 50 && timeProgress.timeElapsedPercentage > 50) {
      recommendations.push({
        type: 'activity',
        priority: 'medium',
        message: 'Activity completion is lagging behind schedule. Consider additional resources.'
      });
    }
    
    // Budget-based recommendations
    if (budgetProgress.isOverBudget) {
      recommendations.push({
        type: 'budget',
        priority: 'high',
        message: `Project is over budget by $${Math.abs(budgetProgress.budgetVariance).toLocaleString()}. Review spending and adjust scope.`
      });
    } else if (budgetProgress.budgetUtilization > 80) {
      recommendations.push({
        type: 'budget',
        priority: 'medium',
        message: 'Budget utilization is high. Monitor remaining expenses carefully.'
      });
    }
    
    // Quality-based recommendations
    if (qualityMetrics.qualityScore < 80) {
      recommendations.push({
        type: 'quality',
        priority: 'medium',
        message: 'Report quality is below standards. Provide additional training or support.'
      });
    }
    
    return recommendations;
  }

  // Generate alerts for critical issues
  static generateAlerts({ timeProgress, activityProgress, budgetProgress, qualityMetrics, project }) {
    const alerts = [];
    
    if (timeProgress.isOverdue) {
      alerts.push({
        type: 'critical',
        category: 'timeline',
        message: `Project "${project.name}" is overdue`
      });
    }
    
    if (budgetProgress.isOverBudget) {
      alerts.push({
        type: 'critical',
        category: 'budget',
        message: `Project "${project.name}" is over budget`
      });
    }
    
    if (activityProgress.overdueActivities > 0) {
      alerts.push({
        type: 'warning',
        category: 'activities',
        message: `${activityProgress.overdueActivities} activities are overdue in project "${project.name}"`
      });
    }
    
    return alerts;
  }

  // Get tracking status for multiple projects
  static async evaluateMultipleProjects(projectIds) {
    const results = [];
    
    for (const projectId of projectIds) {
      try {
        const status = await this.evaluateProjectStatus(projectId);
        results.push(status);
      } catch (error) {
        results.push({
          projectId,
          error: error.message,
          overallStatus: 'unknown'
        });
      }
    }
    
    return results;
  }

  // Get summary of all projects tracking status
  static async getProjectTrackingSummary() {
    try {
      const projects = await Project.find({ status: { $in: ['active', 'pending'] } });
      const projectIds = projects.map(p => p._id);
      
      const trackingResults = await this.evaluateMultipleProjects(projectIds);
      
      const summary = {
        totalProjects: trackingResults.length,
        onTrack: trackingResults.filter(r => r.overallStatus === 'on_track').length,
        warning: trackingResults.filter(r => r.overallStatus === 'warning').length,
        atRisk: trackingResults.filter(r => r.overallStatus === 'at_risk').length,
        critical: trackingResults.filter(r => r.overallStatus === 'critical').length,
        unknown: trackingResults.filter(r => r.overallStatus === 'unknown').length
      };
      
      return {
        summary,
        projects: trackingResults
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = ProjectTrackingService;
