const mongoose = require("mongoose");

const ProjectObjectiveSchema = new mongoose.Schema(
  {
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Project",
      required: [true, "Project reference is required"],
    },
    objective: {
      type: String,
      required: [true, "Please provide the project objective"],
      trim: true,
      minlength: [5, "Objective must be at least 5 characters long"],
      maxlength: [500, "Objective cannot exceed 500 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [1000, "Description cannot exceed 1000 characters"],
    },
    priority: {
      type: String,
      enum: ["high", "medium", "low"],
      default: "medium",
    },
    status: {
      type: String,
      enum: ["pending", "in-progress", "completed"],
      default: "pending",
    },
    targetDate: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Creator reference is required"],
    },
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Index for better query performance
ProjectObjectiveSchema.index({ project: 1 });
ProjectObjectiveSchema.index({ status: 1 });
ProjectObjectiveSchema.index({ priority: 1 });

module.exports = mongoose.model("ProjectObjective", ProjectObjectiveSchema);
