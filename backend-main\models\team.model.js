const mongoose = require("mongoose");

const TeamSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Please provide the team name"],
      trim: true,
      minlength: [3, "Team name must be at least 3 characters long"],
      maxlength: [100, "Team name cannot exceed 100 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, "Description cannot exceed 500 characters"],
      default: "",
    },
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Project",
      required: [true, "Please provide the project for this team"],
    },
    projectManager: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide the project manager for this team"],
    },
    fieldOfficers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    }],
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide who created this team"],
    },
    // Team assignment fields
    assignedTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User", // Project manager who the team is assigned to
    },
    assignedAt: {
      type: Date,
    },
    assignmentMessage: {
      type: String,
      maxlength: [500, "Assignment message cannot exceed 500 characters"],
    },
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Virtual to get team member count
TeamSchema.virtual('memberCount').get(function() {
  return this.fieldOfficers ? this.fieldOfficers.length : 0;
});

// Index for better query performance
TeamSchema.index({ project: 1 });
TeamSchema.index({ projectManager: 1 });
TeamSchema.index({ fieldOfficers: 1 });
TeamSchema.index({ isActive: 1 });

module.exports = mongoose.model("Team", TeamSchema);
