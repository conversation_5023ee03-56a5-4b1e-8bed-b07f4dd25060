{"version": 3, "file": "task-registry.js", "sourceRoot": "", "sources": ["../../src/task-registry.ts"], "names": [], "mappings": ";;;AAEA,MAAM,KAAK,GAAG,IAAI,GAAG,EAA0B,CAAC;AAEhD,MAAa,YAAY;IACvB,GAAG,CAAC,IAAmB;QACrB,IAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAC,CAAC;YACpB,MAAM,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,qBAAqB,CAAC,CAAA;QACnD,CAAC;QAED,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAEzB,IAAI,CAAC,EAAE,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,GAAG,CAAC,MAAc;QAChB,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,IAAmB;QACxB,IAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAC,CAAC;YACpB,IAAI,EAAE,OAAO,EAAE,CAAC;YAChB,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IAED,GAAG;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,GAAG,CAAC,MAAc;QAChB,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAED,OAAO;QACN,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;CACF;AAnCD,oCAmCC"}