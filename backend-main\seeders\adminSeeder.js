const mongoose = require("mongoose");
const User = require("../models/user.model");
const Role = require("../models/roles.model");
const Department = require("../models/department.model");
const District = require("../models/districts.model");

async function createAdmin() {
  try {
    // First, create a default department if it doesn't exist
    let department = await Department.findOne({ name: "Administration" });
    if (!department) {
      department = await Department.create({
        name: "Administration",
        code: "ADMIN"
      });
      console.log("✅ Created department: Administration");
    } else {
      console.log("✅ Department already exists: Administration");
    }

    // Get the admin role
    const adminRole = await Role.findOne({ name: "admin" });
    if (!adminRole) {
      throw new Error("Admin role not found. Please run the roles seeder first.");
    }

    // Get a default district (Lilongwe as capital)
    let district = await District.findOne({ name: "Lilongwe" });
    if (!district) {
      // If Lilongwe doesn't exist, get the first available district
      district = await District.findOne();
      if (!district) {
        throw new Error("No districts found. Please run the districts seeder first.");
      }
    }

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: "<EMAIL>" });
    if (existingAdmin) {
      console.log("✅ Admin user already exists");
      return existingAdmin;
    }

    // Create admin user
    const adminUser = await User.create({
      fullName: "System Administrator",
      email: "<EMAIL>",
      password: "Admin@123", // This will be hashed automatically by the pre-save hook
      phoneNumber: "+265888123456",
      role: adminRole._id,
      district: district._id,
      department: department._id,
      bio: "System administrator with full access to all features",
      experience: 5,
      skills: ["System Administration", "Project Management", "User Management"],
      profilePicture: "/uploads/user-avatars/admin-profile.png"
    });

    console.log("✅ Admin user created successfully!");
    console.log("📧 Email: <EMAIL>");
    console.log("🔑 Password: Admin@123");
    console.log("👤 Name: System Administrator");

    return adminUser;

  } catch (error) {
    console.error("❌ Error creating admin:", error.message);
    throw error;
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  mongoose
    .connect("mongodb://127.0.0.1:27017/sprodeta")
    .then(async () => {
      console.log("🌱 Starting admin seeder...");
      await createAdmin();
      console.log("✅ Admin seeder completed!");
    })
    .catch((err) => {
      console.error("❌ MongoDB Connection Error:", err);
    })
    .finally(() => {
      mongoose.disconnect();
      console.log("🔌 Disconnected from MongoDB");
    });
}

module.exports = { createAdmin }; 