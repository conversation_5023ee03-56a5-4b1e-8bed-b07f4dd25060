const express = require("express");
const {
  createActivityReport,
  getAllReports,
} = require("../controllers/activityReports.controller");
const { protect, authorize } = require("../middleware/auth");

const router = express.Router();

// Field officers can submit reports, managers can view all reports
router.get("/", protect, authorize('projectManager', 'seniorManager', 'admin'), getAllReports);
router.post("/", protect, authorize('fieldOfficer'), createActivityReport);

module.exports = router;
