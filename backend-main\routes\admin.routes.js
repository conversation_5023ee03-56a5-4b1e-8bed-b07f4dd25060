const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const {
  getDashboardStats,
  getActivityLogs,
  exportActivityLogs,
  getActivityStats,
  cleanupOldLogs
} = require('../controllers/admin.controller');

// Protect all routes - only authenticated users
router.use(protect);

// Restrict all routes to admin only
router.use(authorize('admin'));

// Dashboard statistics
router.get('/dashboard/stats', getDashboardStats);

// Activity logs
router.get('/logs', getActivityLogs);
router.get('/logs/export', exportActivityLogs);
router.get('/logs/stats', getActivityStats);
router.delete('/logs/cleanup', cleanupOldLogs);

module.exports = router;
