const { default: mongoose } = require("mongoose");

const ActivityReport = new mongoose.Schema(
  {
    activity: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProjectActivities",
      required: [true, "Please provide the project activity"],
    },
    submittedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide the one who is submitting"],
    },
    submitTo: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide the project manager this report is submitted to"],
    },
    status: {
      type: String,
      enum: ["lateSubmission", "onTime"],
      default: "onTime",
    },
    lateDays: {
      type: Number,
      required: function () {
        return this?.status === "lateSubmission";
      },
    },
    approved: {
      type: Boolean,
      default: false,
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    approvedAt: {
      type: Date,
    },
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    rejectedAt: {
      type: Date,
    },
    rejectionReason: {
      type: String,
    },
    amountSpent: {
      type: Number,
      default: 0,
      min: [0, "Amount spent cannot be negative"],
    },
    content: [
      {
        required: {
          type: Boolean,
          default: false,
        },
        fieldName: {
          type: String,
        },
        entry: {
          type: String,
        },
      },
    ],
  },
  { timestamps: true }
);

module.exports = mongoose.model("ActivityReport", ActivityReport);
