{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "prod": "node server.js", "minimal": "node minimal-server.js", "seed:districts": "node seeders/districtSeeder.js", "seed:roles": "node seeders/rolesSeeder.js", "seed:admin": "node seeders/adminSeeder.js", "seed:all": "npm run seed:roles && npm run seed:districts && npm run seed:admin", "seed": "node seeders/masterSeeder.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "fs": "^0.0.1-security", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "node-cron": "^4.2.1", "nodemailer": "^7.0.2", "path": "^0.12.7", "pdfkit": "^0.17.1", "validator": "^13.15.0", "ws": "^8.18.3", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.1.10"}}