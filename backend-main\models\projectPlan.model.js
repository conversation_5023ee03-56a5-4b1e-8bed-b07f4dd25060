const mongoose = require("mongoose");

const ProjectPlanSchema = new mongoose.Schema(
  {
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Project",
      required: [true, "Please provide the project"],
    },

    // Project plan specific fields
    objectives: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProjectObjective",
    }],

    goals: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProjectGoal",
    }],

    inputs: [{
      name: {
        type: String,
        required: [true, "Please provide the input name"],
        trim: true,
      },
      quantity: {
        type: String,
        required: [true, "Please provide the input quantity"],
        trim: true,
      },
    }],

    outputs: [{
      name: {
        type: String,
        required: [true, "Please provide the output name"],
        trim: true,
      },
      value: {
        type: String,
        required: [true, "Please provide the output value"],
        trim: true,
      },
    }],

    expectedOutcome: {
      type: String,
      required: [true, "Please provide the expected outcome"],
      trim: true,
    },

    activities: {
      type: [{
        title: {
          type: String,
          required: [true, "Please provide the activity title"],
          trim: true,
          minlength: [1, "Activity title cannot be empty"],
        },
        description: {
          type: String,
          required: [true, "Please provide the activity description"],
          trim: true,
          minlength: [1, "Activity description cannot be empty"],
        },
      }],
      default: []
    },

    deliverables: [
      {
        title: {
          type: String,
          required: [true, "Please provide the deliverable title"],
        },
        description: {
          type: String,
          required: [true, "Please provide the deliverable description"],
        },
        deadline: {
          type: Date,
          required: [true, "Please provide the deliverable deadline"],
        },
        type: {
          type: String,
          enum: ["document", "report", "presentation", "software", "hardware", "other"],
          default: "document",
        },
        assignedTo: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        status: {
          type: String,
          enum: ["pending", "inprogress", "submitted", "approved", "rejected"],
          default: "pending",
        },
        priority: {
          type: String,
          enum: ["low", "medium", "high", "critical"],
          default: "medium",
        },
        requirements: [{
          type: String,
          trim: true,
        }],
        acceptanceCriteria: [{
          type: String,
          trim: true,
        }],
        estimatedHours: {
          type: Number,
          min: [0, "Estimated hours cannot be negative"],
        },
        dependencies: [{
          type: String,
          trim: true,
        }],
      },
    ],

    milestones: [
      {
        title: {
          type: String,
          required: [true, "Please provide the milestone title"],
        },
        description: {
          type: String,
          required: [true, "Please provide the milestone description"],
        },
        deadline: {
          type: Date,
          required: [true, "Please provide the milestone deadline"],
        },
        status: {
          type: String,
          enum: ["pending", "inprogress", "completed", "delayed"],
          default: "pending",
        },
        priority: {
          type: String,
          enum: ["low", "medium", "high", "critical"],
          default: "medium",
        },
        assignedTo: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        completedDate: {
          type: Date,
        },
        dependencies: [{
          type: String,
          trim: true,
        }],
        deliverables: [{
          type: String,
          trim: true,
        }],
        successCriteria: [{
          type: String,
          trim: true,
        }],
      },
    ],

    resources: [
      {
        type: {
          type: String,
          required: [true, "Please provide the resource type"],
          enum: ["human", "material", "financial", "equipment"],
        },
        name: {
          type: String,
          required: [true, "Please provide the resource name"],
        },
        quantity: {
          type: Number,
          required: [true, "Please provide the resource quantity"],
        },
        cost: {
          type: Number,
          required: [true, "Please provide the resource cost"],
        },
      },
    ],

    risks: [
      {
        title: {
          type: String,
          required: [true, "Please provide the risk title"],
        },
        description: {
          type: String,
          required: [true, "Please provide the risk description"],
        },
        probability: {
          type: String,
          enum: ["low", "medium", "high"],
          required: [true, "Please provide the risk probability"],
        },
        impact: {
          type: String,
          enum: ["low", "medium", "high"],
          required: [true, "Please provide the risk impact"],
        },
        mitigation: {
          type: String,
          required: [true, "Please provide the risk mitigation strategy"],
        },
      },
    ],

    assumptions: [
      {
        title: {
          type: String,
          required: [true, "Please provide the assumption title"],
        },
        description: {
          type: String,
          required: [true, "Please provide the assumption description"],
        },
      },
    ],

    status: {
      type: String,
      enum: ["draft", "submitted", "approved", "rejected"],
      default: "draft",
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("ProjectPlan", ProjectPlanSchema);
