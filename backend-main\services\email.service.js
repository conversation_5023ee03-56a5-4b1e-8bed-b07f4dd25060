const nodemailer = require("nodemailer");
const { moneyFormat } = require("../utils/moneyFormat");
require("dotenv").config();

const transporter = nodemailer.createTransport({
  secure: true,
  host: process.env.GMAIL_HOST,
  port: process.env.GMAIL_PORT,
  auth: {
    user: process.env.GMAIL_EMAIL,
    pass: process.env.GMAIL_APP_PASSWORD,
  },
});

// Send activity report submission notification to project manager
exports.sendReportSubmissionEmail = async (emailData) => {
  try {
    const { projectManager, fieldOfficer, activity, project, reportId, submissionStatus } = emailData;

    const statusColor = submissionStatus === 'lateSubmission' ? '#ff6b6b' : '#4ecdc4';
    const statusText = submissionStatus === 'lateSubmission' ? 'Late Submission' : 'On Time';

    await transporter.sendMail({
      to: projectManager.email,
      subject: `Activity Report Submitted - ${activity.title}`,
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #b3d9ff; padding: 20px; text-align: center;">
            <h1 style="color: #003366; margin: 0;">📋 Activity Report Submitted</h1>
          </div>
          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Dear ${projectManager.fullName},</p>
            <p style="font-size: 16px; color: #333;">
              A field officer has submitted an activity report that requires your review and approval.
            </p>

            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #003366; margin-top: 0;">Report Details:</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold; width: 30%;">Field Officer:</td>
                  <td style="padding: 8px;">${fieldOfficer.fullName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Email:</td>
                  <td style="padding: 8px;">${fieldOfficer.email}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Activity:</td>
                  <td style="padding: 8px;">${activity.title}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Project:</td>
                  <td style="padding: 8px;">${project.name}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Status:</td>
                  <td style="padding: 8px;">
                    <span style="background-color: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                      ${statusText}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Report ID:</td>
                  <td style="padding: 8px;">${reportId}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Submitted:</td>
                  <td style="padding: 8px;">${new Date().toLocaleString()}</td>
                </tr>
              </table>
            </div>

            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h4 style="color: #856404; margin-top: 0;">⚡ Action Required</h4>
              <p style="color: #856404; margin-bottom: 0;">
                Please log in to the system to review and approve this activity report. The field officer is waiting for your feedback.
              </p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.FRONTEND_URL || 'http://10.136.10.162:3000'}/manager/reports"
                 style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                Review Report
              </a>
            </div>

            <p style="font-size: 14px; color: #666; margin-top: 30px;">
              Best regards,<br>
              <strong>Sprodeta Project Management System</strong>
            </p>
          </div>
          <div style="background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
            This is an automated notification. Please do not reply to this email.
          </div>
        </div>
      </div>
      `
    });

    console.log(`✅ Report submission email sent to ${projectManager.email}`);
  } catch (error) {
    console.error('❌ Failed to send report submission email:', error);
    throw error;
  }
};

exports.newProjectToAccountant = async (to, projectName, budget, startDate, endDate) => {
  try {
    await transporter.sendMail({
      to: to,
      subject: "NEW PROJECT ALERT",
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #b3d9ff; padding: 20px; text-align: center;">
            <h1 style="color: #003366; margin: 0;">Sprodeta - New Project Alert</h1>
          </div>
          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Dear Accountant,</p>
            <p style="font-size: 16px; color: #333;">
              A new project has been created and requires your attention for funds approval.
            </p>

            <h2 style="color: #003366;">Project Details:</h2>
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
              <tr>
                <td style="padding: 8px; background-color: #f0f8ff; font-weight: bold;">Project Name:</td>
                <td style="padding: 8px;">${projectName}</td>
              </tr>
              <tr>
                <td style="padding: 8px; background-color: #f0f8ff; font-weight: bold;">Budget:</td>
                <td style="padding: 8px;">MWK ${moneyFormat(budget)}</td>
              </tr>
              <tr>
                <td style="padding: 8px; background-color: #f0f8ff; font-weight: bold;">Start Date:</td>
                <td style="padding: 8px;">${startDate}</td>
              </tr>
              <tr>
                <td style="padding: 8px; background-color: #f0f8ff; font-weight: bold;">End Date:</td>
                <td style="padding: 8px;">${endDate}</td>
              </tr>
            </table>

            <p style="font-size: 16px; color: #333; margin-top: 20px;">
              Please review and proceed with the necessary funds approval at your earliest convenience.
            </p>

            <div style="text-align: center; margin-top: 30px;">
              <a href="#" style="background-color: #3399ff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">
                Review Project
              </a>
            </div>
          </div>

          <div style="background-color: #e6f2ff; padding: 15px; text-align: center; font-size: 12px; color: #555;">
            This email was sent by Sprodeta Project Management System.
          </div>
        </div>
      </div>
      `,
    });
  } catch (error) {
    console.log(error);
  }
};

exports.sendUserAddedMail = async (to, role, password) => {
  try {
    await transporter.sendMail({
      to: to,
      subject: "WELCOME ABOARD TO SPRODETA",
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #b3d9ff; padding: 20px; text-align: center;">
            <h1 style="color: #003366; margin: 0;">Welcome to Sprodeta</h1>
          </div>
          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Hello,</p>
            <p style="font-size: 16px; color: #333;">
              You have been successfully added to the Sprodeta system. Below are your account details:
            </p>

            <h2 style="color: #003366;">Account Information:</h2>
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; background-color: #f0f8ff">
              <tr>
                <td style="padding: 8px; font-weight: bold;">Role:</td>
                <td style="padding: 8px;">${role}</td>
              </tr>
              <tr>
                <td style="padding: 8px; font-weight: bold;">Temporary Password:</td>
                <td style="padding: 8px;">${password}</td>
              </tr>
            </table>

            <p style="font-size: 16px; color: #333; margin-top: 20px;">
              Please use the above password to log in to your account. For security reasons, it is recommended that you change your password after your first login.
            </p>

            <div style="text-align: center; margin-top: 30px;">
              <a href="#" style="background-color: #3399ff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">
                Login to Your Account
              </a>
            </div>
          </div>

          <div style="background-color: #e6f2ff; padding: 15px; text-align: center; font-size: 12px; color: #555;">
            This email was sent by the Sprodeta Project Management System.
          </div>
        </div>
      </div>
      `,
    });
  } catch (error) {
    console.log(error);
  }
};

exports.sendBudgetApprovalMail = async (to, projectName) => {
  try {
    await transporter.sendMail({
      to: to,
      subject: "Project Budget Approved - Sprodeta",
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #b3d9ff; padding: 20px; text-align: center;">
            <h1 style="color: #003366; margin: 0;">Sprodeta - Budget Approval</h1>
          </div>
          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Dear Senior Team Member,</p>
            <p style="font-size: 16px; color: #333;">
              We are pleased to inform you that the budget for the project <strong style="color: #003366;">${projectName}</strong> has been successfully approved.
            </p>

            <p style="font-size: 16px; color: #333; margin-top: 20px;">
              The project can now proceed as planned. Thank you for your continued leadership and support.
            </p>

            <div style="text-align: center; margin-top: 30px;">
              <a href="#" style="background-color: #3399ff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">
                View Project Details
              </a>
            </div>
          </div>

          <div style="background-color: #e6f2ff; padding: 15px; text-align: center; font-size: 12px; color: #555;">
            This email was sent by the Sprodeta Project Management System.
          </div>
        </div>
      </div>
      `,
    });
  } catch (error) {
    console.log(error);
  }
};

exports.sendProjectAssignmentMail = async (to, projectName) => {
  try {
    await transporter.sendMail({
      to: to,
      subject: "You Have Been Assigned a New Project - Sprodeta",
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #b3d9ff; padding: 20px; text-align: center;">
            <h1 style="color: #003366; margin: 0;">Sprodeta - Project Assignment</h1>
          </div>
          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Dear Project Manager,</p>
            <p style="font-size: 16px; color: #333;">
              We are pleased to inform you that you have been officially assigned to manage the project <strong style="color: #003366;">${projectName}</strong> by the Senior Manager.
            </p>

            <p style="font-size: 16px; color: #333; margin-top: 20px;">
              Please review the project details and begin the necessary preparations. Your leadership is critical to the success of this project.
            </p>

            <div style="text-align: center; margin-top: 30px;">
              <a href="#" style="background-color: #3399ff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">
                View Project Details
              </a>
            </div>
          </div>

          <div style="background-color: #e6f2ff; padding: 15px; text-align: center; font-size: 12px; color: #555;">
            This email was sent by the Sprodeta Project Management System.
          </div>
        </div>
      </div>
      `,
    });
  } catch (error) {
    console.log(error);
  }
};

exports.sendActivityAssignmentMail = async (to, projectName, activity, dueDate) => {
  try {
    await transporter.sendMail({
      to: to,
      subject: "New Project Activity Assigned - Sprodeta",
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #b3d9ff; padding: 20px; text-align: center;">
            <h1 style="color: #003366; margin: 0;">Sprodeta - Activity Assignment</h1>
          </div>
          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Dear Field Officer,</p>
            <p style="font-size: 16px; color: #333;">
              You have been assigned a new activity under the project <strong style="color: #003366;">${projectName}</strong>.
            </p>

            <h2 style="color: #003366;">Activity Details:</h2>
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
              <tr>
                <td style="padding: 8px; background-color: #f0f8ff; font-weight: bold;">Activity:</td>
                <td style="padding: 8px;">${activity}</td>
              </tr>
              <tr>
                <td style="padding: 8px; background-color: #f0f8ff; font-weight: bold;">Due Date:</td>
                <td style="padding: 8px;">${dueDate}</td>
              </tr>
            </table>

            <p style="font-size: 16px; color: #333; margin-top: 20px;">
              Please review the details and ensure timely completion of this activity. Your contribution is vital to the success of the project.
            </p>

            <div style="text-align: center; margin-top: 30px;">
              <a href="#" style="background-color: #3399ff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">
                View Activity Details
              </a>
            </div>
          </div>

          <div style="background-color: #e6f2ff; padding: 15px; text-align: center; font-size: 12px; color: #555;">
            This email was sent by the Sprodeta Project Management System.
          </div>
        </div>
      </div>
      `,
    });
    console.log('✅ Activity assignment email sent successfully');
  } catch (error) {
    console.error('❌ Failed to send activity assignment email:', error);
    throw error;
  }
};

// Enhanced activity assignment email with more details
exports.sendDetailedActivityAssignmentMail = async (emailData) => {
  try {
    const {
      to,
      fieldOfficerName,
      projectName,
      activityTitle,
      activityDescription,
      startDate,
      endDate,
      budget,
      location,
      targetOutcome,
      priority,
      kpis,
      assignedByName
    } = emailData;

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    };

    const getPriorityColor = (priority) => {
      switch(priority?.toLowerCase()) {
        case 'high': return '#ff6b6b';
        case 'medium': return '#ffa726';
        case 'low': return '#66bb6a';
        default: return '#ffa726';
      }
    };

    await transporter.sendMail({
      to: to,
      subject: `New Activity Assignment: ${activityTitle} - Sprodeta`,
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #b3d9ff; padding: 20px; text-align: center;">
            <h1 style="color: #003366; margin: 0;">🎯 New Activity Assignment</h1>
            <p style="color: #003366; margin: 5px 0 0 0; font-size: 14px;">Sprodeta Project Management System</p>
          </div>

          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Dear ${fieldOfficerName || 'Field Officer'},</p>
            <p style="font-size: 16px; color: #333;">
              You have been assigned a new activity by <strong>${assignedByName}</strong> under the project
              <strong style="color: #003366;">${projectName}</strong>.
            </p>

            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <h2 style="color: #003366; margin: 0 0 15px 0; font-size: 18px;">📋 Activity Details</h2>

              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold; width: 30%;">Title:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ddd;">${activityTitle}</td>
                </tr>
                ${activityDescription ? `
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Description:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ddd;">${activityDescription}</td>
                </tr>
                ` : ''}
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Start Date:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ddd;">${formatDate(startDate)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Due Date:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ddd;">${formatDate(endDate)}</td>
                </tr>
                ${budget ? `
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Budget:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ddd;">${formatCurrency(budget)}</td>
                </tr>
                ` : ''}
                ${location ? `
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Location:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ddd;">${location}</td>
                </tr>
                ` : ''}
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Priority:</td>
                  <td style="padding: 8px; border-bottom: 1px solid #ddd;">
                    <span style="background-color: ${getPriorityColor(priority)}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; text-transform: uppercase;">
                      ${priority || 'Medium'}
                    </span>
                  </td>
                </tr>
                ${targetOutcome ? `
                <tr>
                  <td style="padding: 8px; background-color: #e3f2fd; font-weight: bold;">Target Outcome:</td>
                  <td style="padding: 8px;">${targetOutcome}</td>
                </tr>
                ` : ''}
              </table>
            </div>

            ${kpis && kpis.length > 0 ? `
            <div style="margin: 20px 0;">
              <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px; border-bottom: 2px solid #3399ff; padding-bottom: 5px;">📊 Key Performance Indicators (KPIs)</h3>
              <div style="background-color: #f8f9fa; border-radius: 6px; padding: 15px;">
                ${kpis.map((kpi, index) => `
                  <div style="margin-bottom: ${index < kpis.length - 1 ? '15px' : '0'}; padding-bottom: ${index < kpis.length - 1 ? '15px' : '0'}; ${index < kpis.length - 1 ? 'border-bottom: 1px solid #dee2e6;' : ''}">
                    <h4 style="color: #495057; margin: 0 0 8px 0; font-size: 14px; font-weight: bold;">🎯 ${kpi.name}</h4>
                    <p style="color: #28a745; margin: 0 0 5px 0; font-weight: bold;">Target: ${kpi.target}</p>
                    ${kpi.description ? `<p style="color: #6c757d; margin: 0; font-size: 13px; font-style: italic;">${kpi.description}</p>` : ''}
                  </div>
                `).join('')}
              </div>
            </div>
            ` : ''}

            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
              <h3 style="color: #856404; margin: 0 0 10px 0; font-size: 16px;">⚠️ Important Notes</h3>
              <ul style="color: #856404; margin: 0; padding-left: 20px;">
                <li>Please review all activity details carefully</li>
                <li>Ensure timely completion within the specified deadline</li>
                <li>Contact your project manager if you have any questions</li>
                <li>Submit your activity report upon completion</li>
              </ul>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="#" style="background-color: #3399ff; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; display: inline-block; font-weight: bold;">
                📱 View in Dashboard
              </a>
            </div>
          </div>

          <div style="background-color: #e6f2ff; padding: 15px; text-align: center; font-size: 12px; color: #555;">
            <p style="margin: 0;">This email was sent automatically by the Sprodeta Project Management System.</p>
            <p style="margin: 5px 0 0 0;">Please do not reply to this email.</p>
          </div>
        </div>
      </div>
      `,
    });
    console.log('✅ Detailed activity assignment email sent successfully');
  } catch (error) {
    console.error('❌ Failed to send detailed activity assignment email:', error);
    throw error;
  }
};

// Send project report to senior manager
exports.sendProjectReportToSeniorManager = async (emailData) => {
  try {
    const { seniorManager, projectManager, projectName, reportFile } = emailData;

    console.log('📧 Preparing email to senior manager...');
    console.log('📧 Senior Manager:', seniorManager.email);
    console.log('📧 Project Manager:', projectManager.fullName);
    console.log('📧 Project Name:', projectName);
    console.log('📧 Report File:', reportFile ? {
      originalname: reportFile.originalname,
      size: reportFile.size,
      mimetype: reportFile.mimetype,
      hasBuffer: !!reportFile.buffer
    } : 'No file');

    const mailOptions = {
      to: seniorManager.email,
      subject: `Project Report: ${projectName} - From ${projectManager.fullName}`,
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #b3d9ff; padding: 20px; text-align: center;">
            <h1 style="color: #003366; margin: 0;">📊 Project Report Submission</h1>
            <p style="color: #003366; margin: 5px 0 0 0; font-size: 14px;">Sprodeta Project Management System</p>
          </div>
          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Dear ${seniorManager.fullName},</p>
            <p style="font-size: 16px; color: #333;">
              A comprehensive project report has been submitted by Project Manager <strong>${projectManager.fullName}</strong> for your review.
            </p>

            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #003366; margin: 0 0 10px 0;">📋 Report Details</h3>
              <p style="margin: 5px 0; color: #333;"><strong>Project:</strong> ${projectName}</p>
              <p style="margin: 5px 0; color: #333;"><strong>Submitted by:</strong> ${projectManager.fullName}</p>
              <p style="margin: 5px 0; color: #333;"><strong>Submission Date:</strong> ${new Date().toLocaleDateString()}</p>
              <p style="margin: 5px 0; color: #333;"><strong>Report Type:</strong> Comprehensive Project Report with Activity Details</p>
            </div>

            <p style="font-size: 16px; color: #333;">
              The attached PDF report contains:
            </p>
            <ul style="color: #333; padding-left: 20px;">
              <li>Project overview and current status</li>
              <li>Budget utilization details</li>
              <li>Activity reports from field officers</li>
              <li>Progress metrics and achievements</li>
              <li>Detailed activity submissions and approvals</li>
            </ul>

            <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #0ea5e9;">
              <p style="margin: 0; color: #0369a1; font-weight: bold;">📎 Report Attachment</p>
              <p style="margin: 5px 0 0 0; color: #0369a1;">Please find the comprehensive project report attached to this email.</p>
            </div>

            <p style="font-size: 16px; color: #333;">
              Please review the report and provide any feedback or approvals as necessary. If you have any questions about the project progress or need additional information, please don't hesitate to contact the project manager.
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <p style="color: #666; font-size: 14px;">
                This report was generated and sent automatically through the Sprodeta Project Management System.
              </p>
            </div>
          </div>
          <div style="background-color: #f8f9fa; padding: 15px; text-align: center; border-top: 1px solid #e9ecef;">
            <p style="margin: 0; color: #666; font-size: 12px;">
              © 2024 Sprodeta Project Management System. All rights reserved.
            </p>
          </div>
        </div>
      </div>
      `
    };

    // Add attachment if file is provided
    if (reportFile && reportFile.buffer) {
      console.log('📎 Adding attachment to email...');
      mailOptions.attachments = [
        {
          filename: reportFile.originalname || `${projectName}_Project_Report.pdf`,
          content: reportFile.buffer,
          contentType: 'application/pdf'
        }
      ];
      console.log('📎 Attachment added:', reportFile.originalname);
    } else {
      console.log('⚠️ No attachment - reportFile or buffer missing');
    }

    console.log('📧 Sending email...');
    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Project report email sent successfully to senior manager');
    console.log('📧 Email result:', result.messageId);

  } catch (error) {
    console.error('❌ Failed to send project report email:', error);
    console.error('❌ Error details:', error.message);
    throw error;
  }
};

// Send password reset email
exports.sendPasswordResetMail = async (email, fullName, resetUrl, resetToken) => {
  try {
    console.log('📧 Sending password reset email to:', email);
    console.log('🔗 Reset URL:', resetUrl);

    const mailOptions = {
      from: `SPRODETA <${process.env.GMAIL_EMAIL}>`,
      to: email,
      subject: 'Password Reset Request',
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f9ff; padding: 20px;">
        <div style="max-width: 600px; margin: auto; background-color: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); overflow: hidden;">
          <div style="background-color: #4a90e2; padding: 20px; text-align: center;">
            <h1 style="color: white; margin: 0;">Password Reset</h1>
          </div>
          <div style="padding: 20px;">
            <p style="font-size: 16px; color: #333;">Dear ${fullName},</p>
            <p style="font-size: 16px; color: #333;">
              We received a request to reset your password. If you didn't make this request, you can safely ignore this email.
            </p>
            <p style="font-size: 16px; color: #333;">
              To reset your password, click the button below:
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background-color: #4a90e2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">Reset Password</a>
            </div>

            <p style="font-size: 16px; color: #333;">
              If the button doesn't work, you can copy and paste the following link into your browser:
            </p>
            <p style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; word-break: break-all;">
              ${resetUrl}
            </p>

            <p style="font-size: 16px; color: #333;">
              This link will expire in 10 minutes for security reasons.
            </p>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
              <p>If you didn't request a password reset, please contact our support team immediately.</p>
              <p>© ${new Date().getFullYear()} SPRODETA. All rights reserved.</p>
            </div>
          </div>
        </div>
      </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Password reset email sent successfully');
    console.log('📧 Email result:', result.messageId);
    return result;

  } catch (error) {
    console.error('❌ Failed to send password reset email:', error);
    console.error('❌ Error details:', error.message);
    throw error;
  }
};
