const express = require("express");
const {
  createProject,
  createProjectPlan,
  assignProjectToManager,
  approveBudget,
  getAllProjects,
  assignProjectToPM,
  getProjectProgress,
  getBudgetOverview,
  rejectBudget,
  createObjective,
  createGoal,
  migrateProjectStatuses,
  createSampleData,
  getProjectActivities,
  assignActivityToFieldOfficer,
  updateActivityDetails,
  testEndpoint,
  migrateKPIs,
} = require("../controllers/project.controller");
const { protect, authorize } = require("../middleware/auth");

const router = express.Router();

router.post("/", protect, authorize('seniorManager'), createProject);
router.post("/plan", protect, authorize('seniorManager'), (req, res, next) => {
  console.log("🚨 Request hit /api/v1/project/plan route");
  createProjectPlan(req, res, next);
});
router.post("/assign", protect, authorize('seniorManager'), assignProjectToManager);
router.get("/", protect, getAllProjects);
router.put("/:projectId/approve", protect, approveBudget);
router.put("/:projectId/assign/:projectManagerId", protect, assignProjectToPM);
router.put("/:projectId/reject", protect, rejectBudget);
router.get("/progress", protect, getProjectProgress);
router.get("/budget/overview", protect, authorize('seniorManager', 'projectManager', 'accountant', 'admin'), getBudgetOverview);

// Objective and Goal routes
router.post("/objective", protect, authorize('seniorManager', 'projectManager'), createObjective);
router.post("/goal", protect, authorize('seniorManager', 'projectManager'), createGoal);

// Migration route (admin only)
router.post("/migrate-status", protect, authorize('admin', 'seniorManager'), migrateProjectStatuses);

// Sample data creation (development only)
router.post("/create-sample-data", protect, authorize('admin', 'seniorManager'), createSampleData);

// Project Manager Activity Management Routes (also accessible by senior managers)
router.get("/:projectId/activities", protect, authorize('projectManager', 'seniorManager'), getProjectActivities);
router.put("/activity/:activityId/assign", protect, authorize('projectManager'), assignActivityToFieldOfficer);
router.put("/activity/:activityId", protect, authorize('projectManager'), updateActivityDetails);

// Migration route
router.post("/migrate-kpis", protect, authorize('admin', 'seniorManager'), migrateKPIs);

// Test route
router.post("/test", protect, testEndpoint);

module.exports = router;
