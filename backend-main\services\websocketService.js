const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // Map to store authenticated clients
  }

  init(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws'
    });

    this.wss.on('connection', async (ws, req) => {
      try {
        // Extract token from query parameters or headers
        const url = new URL(req.url, `http://${req.headers.host}`);
        const token = url.searchParams.get('token') || req.headers.authorization?.replace('Bearer ', '');

        if (!token) {
          ws.close(1008, 'Authentication required');
          return;
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id).populate('role');

        if (!user) {
          ws.close(1008, 'Invalid user');
          return;
        }

        // Store client with user info
        const clientInfo = {
          ws,
          userId: user._id.toString(),
          role: user.role.name,
          fullName: user.fullName,
          lastPing: Date.now()
        };

        this.clients.set(ws, clientInfo);

        // Send welcome message
        ws.send(JSON.stringify({
          type: 'connection',
          message: 'Connected to real-time updates',
          timestamp: new Date().toISOString()
        }));

        // Handle incoming messages
        ws.on('message', (message) => {
          try {
            const data = JSON.parse(message);
            this.handleMessage(ws, data);
          } catch (error) {
            // Invalid JSON, ignore
          }
        });

        // Handle client disconnect
        ws.on('close', () => {
          this.clients.delete(ws);
        });

        // Handle ping/pong for connection health
        ws.on('pong', () => {
          const client = this.clients.get(ws);
          if (client) {
            client.lastPing = Date.now();
          }
        });

      } catch (error) {
        ws.close(1008, 'Authentication failed');
      }
    });

    // Ping clients every 30 seconds to keep connections alive
    setInterval(() => {
      this.pingClients();
    }, 30000);

    // Clean up dead connections every minute
    setInterval(() => {
      this.cleanupDeadConnections();
    }, 60000);
  }

  handleMessage(ws, data) {
    const client = this.clients.get(ws);
    if (!client) return;

    switch (data.type) {
      case 'ping':
        ws.send(JSON.stringify({ type: 'pong', timestamp: new Date().toISOString() }));
        break;
      case 'subscribe':
        // Handle subscription to specific channels
        this.handleSubscription(ws, data);
        break;
      default:
        // Unknown message type
        break;
    }
  }

  handleSubscription(ws, data) {
    const client = this.clients.get(ws);
    if (!client) return;

    // Initialize subscriptions if not exists
    if (!client.subscriptions) {
      client.subscriptions = new Set();
    }

    // Add subscription channels based on user role and requested channels
    const { channels } = data;
    if (Array.isArray(channels)) {
      channels.forEach(channel => {
        if (this.isChannelAllowed(channel, client.role)) {
          client.subscriptions.add(channel);
        }
      });
    }

    ws.send(JSON.stringify({
      type: 'subscription_confirmed',
      channels: Array.from(client.subscriptions || []),
      timestamp: new Date().toISOString()
    }));
  }

  isChannelAllowed(channel, userRole) {
    const roleChannels = {
      'senior-manager': ['projects', 'budget', 'notifications', 'dashboard'],
      'project-manager': ['activities', 'reports', 'notifications', 'dashboard'],
      'field-officer': ['activities', 'reports', 'notifications'],
      'accountant': ['budget', 'projects', 'notifications'],
      'admin': ['all']
    };

    const allowedChannels = roleChannels[userRole] || [];
    return allowedChannels.includes(channel) || allowedChannels.includes('all');
  }

  pingClients() {
    this.clients.forEach((client, ws) => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      }
    });
  }

  cleanupDeadConnections() {
    const now = Date.now();
    const timeout = 90000; // 90 seconds

    this.clients.forEach((client, ws) => {
      if (now - client.lastPing > timeout || ws.readyState !== WebSocket.OPEN) {
        this.clients.delete(ws);
        if (ws.readyState === WebSocket.OPEN) {
          ws.close();
        }
      }
    });
  }

  // Broadcast to all connected clients
  broadcast(data, channel = null) {
    const message = JSON.stringify({
      ...data,
      timestamp: new Date().toISOString()
    });

    this.clients.forEach((client, ws) => {
      if (ws.readyState === WebSocket.OPEN) {
        // If channel is specified, only send to subscribed clients
        if (channel && client.subscriptions && !client.subscriptions.has(channel)) {
          return;
        }
        ws.send(message);
      }
    });
  }

  // Send to specific user
  sendToUser(userId, data) {
    const message = JSON.stringify({
      ...data,
      timestamp: new Date().toISOString()
    });

    this.clients.forEach((client, ws) => {
      if (client.userId === userId && ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    });
  }

  // Send to users with specific role
  sendToRole(role, data, channel = null) {
    const message = JSON.stringify({
      ...data,
      timestamp: new Date().toISOString()
    });

    this.clients.forEach((client, ws) => {
      if (client.role === role && ws.readyState === WebSocket.OPEN) {
        // If channel is specified, only send to subscribed clients
        if (channel && client.subscriptions && !client.subscriptions.has(channel)) {
          return;
        }
        ws.send(message);
      }
    });
  }

  // Notify about new notifications
  notifyNewNotification(userId, notification) {
    this.sendToUser(userId, {
      type: 'new_notification',
      data: notification
    });
  }

  // Notify about dashboard updates
  notifyDashboardUpdate(role, updateType, data) {
    this.sendToRole(role, {
      type: 'dashboard_update',
      updateType,
      data
    }, 'dashboard');
  }

  // Notify about project updates
  notifyProjectUpdate(projectId, updateType, data) {
    this.broadcast({
      type: 'project_update',
      projectId,
      updateType,
      data
    }, 'projects');
  }

  // Notify about activity updates
  notifyActivityUpdate(activityId, updateType, data) {
    this.broadcast({
      type: 'activity_update',
      activityId,
      updateType,
      data
    }, 'activities');
  }

  // Notify about budget updates
  notifyBudgetUpdate(projectId, data) {
    this.broadcast({
      type: 'budget_update',
      projectId,
      data
    }, 'budget');
  }

  // Get connected clients count
  getConnectedClientsCount() {
    return this.clients.size;
  }

  // Get clients by role
  getClientsByRole(role) {
    const clients = [];
    this.clients.forEach((client) => {
      if (client.role === role) {
        clients.push({
          userId: client.userId,
          fullName: client.fullName,
          lastPing: client.lastPing
        });
      }
    });
    return clients;
  }
}

// Export singleton instance
module.exports = new WebSocketService();
