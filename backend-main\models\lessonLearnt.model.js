const { default: mongoose } = require("mongoose");

const LessonLearntSchema = new mongoose.Schema(
  {
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Project",
      required: [true, "Please provide the project"],
    },
    description: {
      type: String,
      required: [true, "Please provid the lesson learnt description"],
    },
    capturedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide the one who captured it"],
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("Lesson", LessonLearntSchema);
