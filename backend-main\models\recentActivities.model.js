const mongoose = require("mongoose");

const recentActivitiesSchema = new mongoose.Schema({
  user: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  action: {
    type: String,
    required: true,
    enum: [
      'project_created',
      'project_updated',
      'project_approved',
      'project_rejected',
      'activity_created',
      'activity_assigned',
      'activity_completed',
      'activity_report_submitted',
      'activity_report_approved',
      'user_login',
      'user_logout',
      'user_created',
      'user_updated',
      'user_deleted',
      'user_role_changed',
      'password_reset',
      'budget_updated',
      'status_changed',
      'team_assigned'
    ]
  },
  description: { type: String, required: true },
  entityType: {
    type: String,
    enum: ['project', 'activity', 'user', 'report', 'system'],
    required: true
  },
  entityId: { type: mongoose.Schema.Types.ObjectId },
  entityName: { type: String }, // Name of the project, activity, etc.
  metadata: {
    oldValue: { type: mongoose.Schema.Types.Mixed },
    newValue: { type: mongoose.Schema.Types.Mixed },
    additionalInfo: { type: String }
  },
  ipAddress: { type: String },
  userAgent: { type: String },
}, { timestamps: true });

// Index for better query performance
recentActivitiesSchema.index({ user: 1, createdAt: -1 });
recentActivitiesSchema.index({ action: 1, createdAt: -1 });
recentActivitiesSchema.index({ entityType: 1, entityId: 1 });

const recentActivities = mongoose.model("Activity", recentActivitiesSchema);

module.exports = recentActivities;
