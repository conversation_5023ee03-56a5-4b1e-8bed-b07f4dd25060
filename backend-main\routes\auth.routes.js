const express = require("express");
const {
  login,
  logout,
  getMe,
  testAuth,
  forgotPassword,
  resetPassword
} = require("../controllers/auth.controller");
const { protect } = require("../middleware/auth");

const router = express.Router();

router.post("/login", login);
router.post("/logout", protect, logout);
router.get("/me", protect, getMe);
router.get("/test", protect, testAuth);

// Add a simple test endpoint without authentication
router.get("/health", (req, res) => {
  res.status(200).json({
    status: "success",
    message: "Auth routes are working",
    timestamp: new Date().toISOString()
  });
});

// Debug endpoint to check available users (remove in production)
router.get("/debug/users", async (req, res) => {
  try {
    const User = require("../models/user.model");
    const users = await User.find({}).populate("role", "name").select("email fullName role");
    res.status(200).json({
      status: "success",
      users: users.map(user => ({
        email: user.email,
        fullName: user.fullName,
        role: user.role?.name
      }))
    });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch users",
      error: error.message
    });
  }
});

// Password reset routes
router.post("/forgot-password", forgotPassword);
router.post("/reset-password", resetPassword);

module.exports = router;