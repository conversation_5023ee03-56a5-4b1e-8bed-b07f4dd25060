const express = require("express");
const {
  addUser,
  getAllUsers,
  getMe,
  updateProfile,
  updateUser,
  deleteUser,
  resetUserPassword,
  getFieldOfficers,
  getUsersByRole,
  getAllRoles
} = require("../controllers/user.controller");
const { protect, authorize } = require("../middleware/auth");
const multer = require('multer');
const path = require('path');
const User = require('../models/user.model');

const router = express.Router();

const {
  getUserSettings,
  updateUserSettings,
  changePassword,
} = require('../controllers/user.controller');


const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, req.user.id + path.extname(file.originalname));
  }
});
const upload = multer({ storage });



router.post("/", addUser);
router.get("/", getAllUsers);
router.get("/me", protect, getMe);
router.patch("/me", protect, updateProfile);

// Role-specific user routes
router.get("/field-officers", protect, authorize('projectManager', 'seniorManager', 'admin'), getFieldOfficers);
router.get("/role/:roleName", protect, authorize('projectManager', 'seniorManager', 'admin'), getUsersByRole);
router.get("/roles", protect, getAllRoles);
router.patch('/me/avatar', protect, upload.single('avatar'), async (req, res) => {
  if (!req.file) return res.status(400).json({ message: 'No file uploaded' });
  const user = await User.findByIdAndUpdate(
    req.user.id,
    { profilePicture: `/uploads/${req.file.filename}` },
    { new: true }
  );
  res.json({ profilePicture: user.profilePicture });
});
router.get('/settings', protect, getUserSettings);
router.put('/settings', protect, updateUserSettings);
router.post("/change-password", protect, changePassword);

// Admin routes for user management
router.patch("/:userId", protect, authorize('admin'), updateUser);
router.delete("/:userId", protect, authorize('admin'), deleteUser);
router.post("/:userId/reset-password", protect, authorize('admin'), resetUserPassword);

module.exports = router;
