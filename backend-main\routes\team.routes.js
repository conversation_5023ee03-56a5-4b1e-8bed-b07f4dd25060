const express = require("express");
const {
  createTeam,
  getAllTeams,
  getTeamById,
  updateTeam,
  deleteTeam,
  assignTeamToManager,
  // Project Manager team management
  getProjectsForTeamCreation,
  getFieldOfficersByProjectLocation,
  createProjectTeam,
  getProjectManagerTeams,
  getTeamFieldOfficersForProject,
} = require("../controllers/team.controller");
const { protect, authorize } = require("../middleware/auth");

const router = express.Router();

// All team routes require authentication
router.use(protect);

// Create team - only admins can create teams
router.post("/", authorize('admin'), createTeam);

// Get all teams - admins can see all, project managers can see their teams
router.get("/", getAllTeams);

// Get teams assigned to the current project manager
router.get("/assigned", async (req, res) => {
  try {
    const teams = await require("../models/team.model").find({
      assignedTo: req.user.id,
      isActive: true
    })
    .populate('projectManager', 'fullName email')
    .populate('fieldOfficers', 'fullName email phoneNumber')
    .populate('createdBy', 'fullName email')
    .sort({ assignedAt: -1 });

    res.status(200).json({
      status: "success",
      data: teams
    });
  } catch (error) {
    console.error('❌ Error fetching assigned teams:', error);
    res.status(500).json({
      status: "failed",
      message: "Failed to fetch assigned teams"
    });
  }
});

// Get specific team by ID
router.get("/:id", getTeamById);

// Update team - only admins can update teams
router.put("/:id", authorize('admin'), updateTeam);

// Delete team - only admins can delete teams
router.delete("/:id", authorize('admin'), deleteTeam);

// Assign team to project manager - only admins can assign teams
router.post("/:teamId/assign", authorize('admin'), assignTeamToManager);

// ========== PROJECT MANAGER TEAM MANAGEMENT ROUTES ==========

// Test route to verify project manager team routes are working
router.get("/manager/test", protect, async (req, res) => {
  try {
    const Project = require('../models/project.model');
    const allProjects = await Project.find({})
      .populate('assignedTo', 'fullName email')
      .select('title status assignedTo');

    res.status(200).json({
      status: "success",
      message: "Project manager team routes are working",
      user: req.user?.id,
      userRole: req.user?.role?.name || req.user?.role,
      timestamp: new Date().toISOString(),
      debug: {
        totalProjects: allProjects.length,
        projectsAssignedToUser: allProjects.filter(p =>
          p.assignedTo && (p.assignedTo._id.toString() === req.user.id || p.assignedTo._id === req.user.id)
        ).length,
        allProjectsInfo: allProjects.map(p => ({
          title: p.title,
          status: p.status,
          assignedTo: p.assignedTo ? {
            id: p.assignedTo._id.toString(),
            name: p.assignedTo.fullName
          } : null
        }))
      }
    });
  } catch (error) {
    res.status(200).json({
      status: "success",
      message: "Project manager team routes are working",
      user: req.user?.id,
      userRole: req.user?.role?.name || req.user?.role,
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// Get projects for team creation (project managers only)
router.get("/manager/projects", authorize('projectManager'), getProjectsForTeamCreation);

// Test endpoint for debugging field officers issue
router.get("/manager/project/:projectId/debug", authorize('projectManager'), async (req, res) => {
  try {
    const { projectId } = req.params;
    const projectManagerId = req.user.id;

    const Project = require('../models/project.model');
    const User = require('../models/user.model');
    const Role = require('../models/roles.model');

    console.log('🔍 Debug endpoint - Project ID:', projectId);
    console.log('🔍 Debug endpoint - Manager ID:', projectManagerId);

    // Check project
    const project = await Project.findById(projectId).populate('location', 'name').populate('assignedTo', 'fullName email');

    // Check role
    const fieldOfficerRole = await Role.findOne({ name: 'fieldOfficer' });

    // Check users in location
    const usersInLocation = project?.location ? await User.find({
      district: project.location._id
    }).populate('role', 'name').populate('district', 'name') : [];

    // Check field officers specifically
    const fieldOfficers = project?.location && fieldOfficerRole ? await User.find({
      role: fieldOfficerRole._id,
      district: project.location._id,
      isActive: { $ne: false }
    }).populate('district', 'name') : [];

    res.json({
      status: "success",
      debug: {
        projectId,
        projectManagerId,
        project: project ? {
          id: project._id,
          title: project.title,
          location: project.location,
          assignedTo: project.assignedTo
        } : null,
        fieldOfficerRole: fieldOfficerRole ? {
          id: fieldOfficerRole._id,
          name: fieldOfficerRole.name
        } : null,
        usersInLocationCount: usersInLocation.length,
        usersInLocation: usersInLocation.map(u => ({
          id: u._id,
          name: u.fullName,
          role: u.role?.name,
          district: u.district?.name
        })),
        fieldOfficersCount: fieldOfficers.length,
        fieldOfficers: fieldOfficers.map(fo => ({
          id: fo._id,
          name: fo.fullName,
          district: fo.district?.name
        }))
      }
    });
  } catch (error) {
    console.error('❌ Debug endpoint error:', error);
    res.status(500).json({
      status: "failed",
      message: error.message,
      stack: error.stack
    });
  }
});

// Get field officers by project location (project managers only)
router.get("/manager/project/:projectId/field-officers", authorize('projectManager'), getFieldOfficersByProjectLocation);

// Create team for a specific project (project managers only)
router.post("/manager/create", authorize('projectManager'), createProjectTeam);

// Get teams created by project manager
router.get("/manager/my-teams", authorize('projectManager'), getProjectManagerTeams);

// Get field officers from team for a specific project (for activity assignment)
router.get("/manager/project/:projectId/team-officers", authorize('projectManager'), getTeamFieldOfficersForProject);

// Debug endpoint to check teams for a project
router.get("/manager/project/:projectId/teams-debug", authorize('projectManager'), async (req, res) => {
  try {
    const { projectId } = req.params;
    const projectManagerId = req.user.id;

    const Team = require('../models/team.model');

    // Get all teams for this project
    const allTeams = await Team.find({ project: projectId })
      .populate('projectManager', 'fullName email')
      .populate('createdBy', 'fullName email')
      .populate('fieldOfficers', 'fullName email');

    // Get teams for this manager
    const managerTeams = await Team.find({ projectManager: projectManagerId })
      .populate('project', 'title')
      .populate('fieldOfficers', 'fullName email');

    res.json({
      status: "success",
      debug: {
        projectId,
        projectManagerId,
        allTeamsForProject: allTeams.map(t => ({
          id: t._id,
          name: t.name,
          projectManager: t.projectManager,
          createdBy: t.createdBy,
          fieldOfficersCount: t.fieldOfficers.length,
          isActive: t.isActive
        })),
        allTeamsForManager: managerTeams.map(t => ({
          id: t._id,
          name: t.name,
          project: t.project,
          fieldOfficersCount: t.fieldOfficers.length,
          isActive: t.isActive
        }))
      }
    });
  } catch (error) {
    res.status(500).json({
      status: "failed",
      message: error.message
    });
  }
});

module.exports = router;
