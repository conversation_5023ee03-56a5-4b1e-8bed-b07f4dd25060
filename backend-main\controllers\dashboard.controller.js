const Project = require("../models/project.model");
const ActivityReport = require("../models/activityReport.model");
const User = require("../models/user.model");
const Role = require("../models/roles.model");
const ProjectActivities = require("../models/projectActivities.model");

// 🟦 Senior Manager Stats
exports.getSeniorManagerStats = async (req, res) => {
  try {
    // Count projects by status (updated for new enum values)
    const totalProjects = await Project.countDocuments({
      status: { $in: ["inprogress", "completed"] }
    }); // All projects
    const completedProjects = await Project.countDocuments({
      status: "completed",
    });
    const ongoingProjects = await Project.countDocuments({ status: "inprogress" }); // In progress projects

    // Count team members (project managers assigned to projects)
    const projectManagerRoleId = await getRoleId("project-manager");
    const teamMembers = await User.countDocuments({
      role: projectManagerRoleId,
    });

    // Get recent projects with assignedTo populated and calculate progress
    // Only get projects with valid status values
    const recentProjects = await Project.find({
      status: { $in: ["inprogress", "completed"] }
    })
      .limit(5)
      .sort({ createdAt: -1 })
      .populate('assignedTo', 'fullName')
      .lean();

    console.log("📊 Recent projects found:", recentProjects.length);
    console.log("📊 Recent projects statuses:", recentProjects.map(p => ({ title: p.title, status: p.status })));

    // Calculate progress for each recent project
    const recentProjectsWithProgress = await Promise.all(
      recentProjects.map(async (project) => {
        // Get all activities for this project
        const totalActivities = await ProjectActivities.countDocuments({
          project: project._id,
        });
        const completedActivities = await ProjectActivities.countDocuments({
          project: project._id,
          status: "completed",
        });
        const inProgressActivities = await ProjectActivities.countDocuments({
          project: project._id,
          status: "inprogress",
        });

        // Calculate progress percentage based on activities
        let progress = 0;
        if (totalActivities > 0) {
          // Completed activities count as 100%, in-progress as 50%
          const weightedProgress = (completedActivities * 100) + (inProgressActivities * 50);
          progress = Math.round(weightedProgress / (totalActivities * 100) * 100);
        }

        return {
          ...project,
          progress,
          totalActivities,
          completedActivities,
          inProgressActivities,
        };
      })
    );

    // Get project progress data for all in-progress projects
    const activeProjects = await Project.find({
      status: "inprogress"
    })
      .select('title')
      .lean();

    console.log("📈 Active projects for progress:", activeProjects.length);
    console.log("📈 Active projects:", activeProjects.map(p => p.title));

    const projectProgressData = await Promise.all(
      activeProjects.map(async (project) => {
        const totalActivities = await ProjectActivities.countDocuments({
          project: project._id,
        });
        const completedActivities = await ProjectActivities.countDocuments({
          project: project._id,
          status: "completed",
        });
        const inProgressActivities = await ProjectActivities.countDocuments({
          project: project._id,
          status: "inprogress",
        });

        // Calculate weighted progress
        let progress = 0;
        if (totalActivities > 0) {
          const weightedProgress = (completedActivities * 100) + (inProgressActivities * 50);
          progress = Math.round(weightedProgress / (totalActivities * 100) * 100);
        }

        return {
          title: project.title || project.name,
          name: project.title || project.name, // For backward compatibility
          progress,
          totalActivities,
          completedActivities,
        };
      })
    );

    // Check and update project completion status
    await checkAndUpdateProjectCompletion();

    console.log("📊 Recent projects with progress:", recentProjectsWithProgress.length);
    console.log("📈 Project progress data:", projectProgressData.length);

    // Get recent activities
    const recentActivities = await ProjectActivities.find()
      .populate('project', 'title')
      .populate('assignedTo', 'fullName')
      .populate('createdBy', 'fullName')
      .sort({ createdAt: -1 })
      .limit(5)
      .lean();

    console.log("🔄 Recent activities:", recentActivities.length);

    const responseData = {
      totalProjects,
      completedProjects,
      ongoingProjects,
      teamMembers,
      recentProjects: recentProjectsWithProgress,
      projectProgress: projectProgressData,
      recentActivities: recentActivities,
    };

    console.log("📤 Sending dashboard response:", {
      totalProjects,
      completedProjects,
      ongoingProjects,
      teamMembers,
      recentProjectsCount: recentProjectsWithProgress.length,
      projectProgressCount: projectProgressData.length,
      recentActivitiesCount: recentActivities.length
    });

    return res.status(200).json({
      status: "success",
      data: responseData
    });
  } catch (error) {
    console.error("Senior Manager Dashboard Error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong! Please try again.",
    });
  }
};

// 🟨 Field Officer Stats (existing logic adapted)
exports.getFieldOfficerStats = async (req, res) => {
  try {
    console.log('🔄 Dashboard - Getting field officer stats for user:', req.user?.id);

    const userId = req.user.id;
    const fieldOfficerRole = await Role.findOne({ name: "field-officer" });
    console.log('📋 Field officer role found:', fieldOfficerRole?._id);

    // Get activities assigned to this field officer
    const assignedActivities = await ProjectActivities.find({ assignedTo: userId });
    const totalAssignedActivities = assignedActivities.length;

    // Count completed activities for this field officer
    const completedActivities = assignedActivities.filter(activity =>
      activity.status === 'completed'
    ).length;

    // Count active activities for this field officer
    const activeActivities = assignedActivities.filter(activity =>
      activity.status === 'active' || activity.status === 'inprogress'
    ).length;

    // Count pending reports submitted by this field officer
    const pendingReports = await ActivityReport.countDocuments({
      submittedBy: userId,
      approved: { $ne: true },
      rejectedBy: { $exists: false }
    });

    // Count field officers in the same location/area (team members)
    const currentUser = await User.findById(userId);
    const teamMembers = await User.countDocuments({
      role: fieldOfficerRole?._id,
      location: currentUser?.location // Assuming location field exists
    });

    const stats = {
      totalAssignedActivities,
      activeActivities,
      completedActivities,
      pendingReports,
      teamMembers,
      projectProgress: totalAssignedActivities > 0 ?
        Math.round((completedActivities / totalAssignedActivities) * 100) : 0
    };

    console.log('✅ Dashboard - Field officer stats calculated:', stats);

    res.status(200).json({
      status: "success",
      data: stats,
    });
  } catch (error) {
    console.error("Field Officer Dashboard Error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong! Please try again.",
    });
  }
};

exports.getProjectManagerStats = async (req, res) => {
  try {
    // Count all activities
    const totalActivities = await ProjectActivities.countDocuments();

    // Count in-progress activities
    const inProgress = await ProjectActivities.countDocuments({
      status: "in-progress",
    });

    // Count completed activities
    const completed = await ProjectActivities.countDocuments({
      status: "completed",
    });

    // Count all users with the role of "field-officer"
    const teamMembers = await User.countDocuments({
      role: await getRoleId("field-officer"),
    });

    const stats = {
      totalActivities,
      inProgress,
      completed,
      teamMembers,
    };

    return res.status(200).json({
      status: "success",
      data: stats,
    });
  } catch (error) {
    console.error("Project Manager Dashboard Error:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong! Please try again.",
    });
  }
};

exports.getAccountantStats = async (req, res) => {
  try {
    const approvedProjects = await Project.find({ status: 'active' });
    const reports = await ActivityReport.find({ status: 'approved' });

    const totalBudget = approvedProjects.reduce((sum, p) => sum + (p.budget || 0), 0);
    const totalDisbursed = reports.reduce((sum, r) => sum + (r.amountSpent || 0), 0);
    const pendingApprovals = await Project.countDocuments({ status: 'pending' });

    const utilizationRate = totalBudget > 0
      ? ((totalDisbursed / totalBudget) * 100).toFixed(1)
      : '0';

    res.status(200).json({
      data: {
        totalBudget,
        totalDisbursed,
        pendingApprovals,
        utilizationRate
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Failed to fetch accountant stats', error });
  }
};

// 🟦 Budget Analytics for Senior Manager
exports.getBudgetAnalytics = async (req, res) => {
  try {
    // Get all projects with their budgets
    const projects = await Project.find({
      status: { $in: ["inprogress", "completed"] }
    }).select('title initialBudget status');

    // Calculate total budget
    const totalBudget = projects.reduce((sum, project) => {
      return sum + (project.initialBudget || 0);
    }, 0);

    // Get all activity reports to calculate used budget
    const ActivityReport = require("../models/activityReport.model");
    const reports = await ActivityReport.find({ status: 'approved' });

    const totalUsed = reports.reduce((sum, report) => {
      return sum + (report.amountSpent || 0);
    }, 0);

    const totalRemaining = totalBudget - totalUsed;
    const overallUtilization = totalBudget > 0
      ? Math.round((totalUsed / totalBudget) * 100)
      : 0;

    res.status(200).json({
      status: "success",
      data: {
        totalBudget,
        totalUsed,
        totalRemaining,
        overallUtilization,
        projectCount: projects.length
      }
    });
  } catch (error) {
    console.error("Error fetching budget analytics:", error);
    res.status(500).json({
      status: "failed",
      message: "Something went wrong!! Please try again",
    });
  }
};

// helper function to get role ID
async function getRoleId(roleName) {
  const Role = require("../models/roles.model");
  const role = await Role.findOne({ name: roleName });
  return role ? role._id : null;
}

// Function to check and update project completion status
async function checkAndUpdateProjectCompletion() {
  try {
    // Get all in-progress projects (corrected from "active" to "inprogress")
    const activeProjects = await Project.find({ status: "inprogress" });

    for (const project of activeProjects) {
      // Count total activities for this project
      const totalActivities = await ProjectActivities.countDocuments({
        project: project._id,
      });

      // Count completed activities for this project
      const completedActivities = await ProjectActivities.countDocuments({
        project: project._id,
        status: "completed",
      });

      // If all activities are completed and there are activities, mark project as completed
      if (totalActivities > 0 && completedActivities === totalActivities) {
        await Project.findByIdAndUpdate(project._id, {
          status: "completed",
        });

        // Send notifications about project completion
        const NotificationService = require('../services/notificationService');
        await NotificationService.notifyProjectCompletion(project._id);

        console.log(`✅ Project ${project.title || project.name} marked as completed - all ${totalActivities} activities completed`);
      }
    }
  } catch (error) {
    console.error("Error checking project completion:", error);
  }
}
