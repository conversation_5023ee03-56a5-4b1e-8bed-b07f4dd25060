const mongoose = require("mongoose");

const ProjectGoals = new mongoose.Schema(
  {
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Project",
      required: [true, "Please provide the project reference"],
    },
    description: {
      type: String,
      required: [true, "Please provide the goal description"],
      trim: true,
      minlength: [5, "Goal description must be at least 5 characters long"],
      maxlength: [500, "Goal description cannot exceed 500 characters"],
    },
    priority: {
      type: String,
      enum: ["high", "medium", "low"],
      default: "medium",
    },
    status: {
      type: String,
      enum: ["pending", "inprogress", "completed"],
      default: "pending",
    },
    targetDate: {
      type: Date,
    },
    measurableOutcome: {
      type: String,
      trim: true,
      maxlength: [300, "Measurable outcome cannot exceed 300 characters"],
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Please provide the user who created this goal"],
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better query performance
ProjectGoals.index({ project: 1 });
ProjectGoals.index({ status: 1 });
ProjectGoals.index({ priority: 1 });
ProjectGoals.index({ createdBy: 1 });

module.exports = mongoose.model("ProjectGoal", ProjectGoals, "projectgoals");
